# 🚀 Live Deployment Guide - Guideway Consulting

## Quick Deployment Steps

### 1. 🚂 Deploy Backend to Railway

1. **Install Railway CLI:**
   ```bash
   npm install -g @railway/cli
   ```

2. **Login and Setup:**
   ```bash
   railway login
   cd backend
   railway link  # Link to existing project or create new
   ```

3. **Set Environment Variables in Railway Dashboard:**
   Go to Railway Dashboard → Your Project → Variables and add:
   ```
   NODE_ENV=production
   PORT=8080
   MONGO_URL=mongodb+srv://abhisheksingh4115:<EMAIL>/Practice-db?retryWrites=true&w=majority
   SECRET_KEY=iREJP1r+kdPWJ7VWcz/BtXibAWhFTEIKnWdGiZ9ZAI4=
   ADMIN_USER=<EMAIL>
   ADMIN_HASHED_PASS=$2b$10$kthSu.4WHdClXAhEdcEbTeChxrfKC0hqidI3sDJv3uDTTa56KjbR2
   CLOUDINARY_CLOUD_NAME=drujwoine
   CLOUDINARY_API_KEY=683974564338127
   CLOUDINARY_API_SECRET=J8UG31wXEm-reuECSmdmQbSy3c4
   FRONTEND_URL=https://guideway-consulting.vercel.app
   RECAPTCHA_SECRET_KEY=6Ld-zC4rAAAAAAVVbpXmfeq9st9SZh0nT-BAceSS
   GOOGLE_CLIENT_ID=************-43j6p7k79nfi39d5u7knmai9nkla1tgb.apps.googleusercontent.com
   BREVO_SMTP_HOST=smtp-relay.brevo.com
   BREVO_SMTP_PORT=587
   BREVO_SMTP_USER=<EMAIL>
   BREVO_SMTP_PASS=6rnTYQPSdMafC8Wq
   BREVO_SEND_EMAIL=<EMAIL>
   BREVO_API_KEY=xkeysib-9ac78f59c620393b66c53ef7c0d0b12f59c4b20ddb58c9a40f112ba2d96ac7-vw0bNZ2V2DccA7iK
   STRIPE_SECRET_KEY=sk_test_51RU2HJPFPsuRFy0ndsdzUcivQi0Q2PR4DCjtyRBsloQSfCAI1COvf9O4PV4tCnjk93sOpsuQJa5x3NdybecqrWAa00IYq3WbZN
   STRIPE_WEBHOOK_SECRET=whsec_9rCtrGn1a9YQrals5gpJWjgdErLwZSdv
   ```

4. **Deploy:**
   ```bash
   railway up
   ```

5. **Monitor:**
   ```bash
   railway logs
   ```

### 2. 🌐 Deploy Frontend to Vercel

1. **Install Vercel CLI:**
   ```bash
   npm install -g vercel
   ```

2. **Login and Deploy:**
   ```bash
   vercel login
   cd frontend
   vercel --prod
   ```

3. **Set Environment Variables in Vercel Dashboard:**
   Go to Vercel Dashboard → Your Project → Settings → Environment Variables:
   ```
   VITE_BACKEND_URL=https://guideway-consulting-production.up.railway.app/api
   VITE_RECAPTCHA_SITE_KEY=6Ld-zC4rAAAAAPjAuASmaRY-J4hie_YZPF63OuHo
   VITE_GOOGLE_CLIENT_ID=************-43j6p7k79nfi39d5u7knmai9nkla1tgb.apps.googleusercontent.com
   ```

## 🔗 Expected URLs

- **Frontend:** https://guideway-consulting.vercel.app
- **Backend:** https://guideway-consulting-production.up.railway.app
- **Backend Health Check:** https://guideway-consulting-production.up.railway.app/health

## 🧪 Testing After Deployment

1. **Test Backend Health:**
   ```bash
   curl https://guideway-consulting-production.up.railway.app/health
   ```

2. **Test Frontend:**
   - Visit: https://guideway-consulting.vercel.app
   - Check if pages load correctly
   - Test signup/login functionality

3. **Test API Connection:**
   - Open browser dev tools on frontend
   - Check if API calls are reaching the backend
   - Verify no CORS errors

## 🔧 Troubleshooting

### If Backend Deployment Fails:
- Check Railway logs: `railway logs`
- Verify all environment variables are set
- Check MongoDB connection string

### If Frontend Can't Connect to Backend:
- Verify VITE_BACKEND_URL in Vercel environment variables
- Check CORS settings in backend
- Test backend health endpoint

### If 404 Errors on Frontend Routes:
- Ensure vercel.json is in the root of frontend
- Check that _redirects file exists in public folder

## 📋 Deployment Checklist

- [ ] Railway CLI installed and logged in
- [ ] Vercel CLI installed and logged in
- [ ] All backend environment variables set in Railway
- [ ] All frontend environment variables set in Vercel
- [ ] Backend deployed and health check passing
- [ ] Frontend deployed and accessible
- [ ] API connections working between frontend and backend
- [ ] No CORS errors in browser console
- [ ] Basic functionality tested (signup, login, etc.)

## 🎯 Next Steps After Deployment

1. Test all major features
2. Check performance and loading times
3. Verify email functionality
4. Test payment integration (if applicable)
5. Monitor error logs for any issues
