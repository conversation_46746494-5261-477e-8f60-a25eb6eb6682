# 🚀 Matchably Frontend (React + Vite)

This is the official frontend for **Matchably**, a modern UGC and campaign management platform.  
Built using **React**, **Vite**, **TailwindCSS**, and other modern tools for fast, scalable development.

---

## 🧱 Tech Stack

- ⚛️ React 18
- ⚡ Vite
- 🎨 Tailwind CSS
- 🧪 React Testing Library / Jest
- 🔍 ESLint + Prettier
- 📦 Redux Toolkit (optional based on usage)
- 🌐 Axios for API integration
- 📁 Organized modular folder structure

---

## 📦 Installation

```bash
git clone https://github.com/Nick9311/Guideway-Consulting.git
cd frontend
cd backend
npm install
