# Guideway Consulting Backend

## Tech Stack
- Node.js
- MongoDB
- <PERSON><PERSON>peteer (for AI Generate)
- Cloudinary
- Stripe
- SMTP (Gmail/Brevo)
- Deployed on Render

## Local Development

### Prerequisites
- Node.js >= 16
- MongoDB Atlas or local MongoDB
- .env file with all required environment variables

### Setup
```bash
cd backend
npm install
```

### Running Locally
```bash
npm start
```

### Environment Variables
Create a `.env` file in this directory and fill in all required values, including:
- `MONGO_URL`
- `SECRET_KEY`
- `OPENAI_API_KEY`
- SMTP, Cloudinary, Stripe, etc.

### Building Frontend for Production
If serving frontend from backend, build frontend and copy `dist` to `backend/dist`.

## Deployment
- Backend: Render.com
- Frontend: Vercel.com

## Contact
For issues, see project management documentation or contact the team.# guide-backend
