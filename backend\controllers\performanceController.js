const { PerformanceMetrics, Campaign, campaignSubmission, User } = require('../database');
const { scrapeWithRetry } = require('../services/scraperService');
const { getRateForCategory } = require('../utils/emvRates');
const { Parser } = require('json2csv');


function getRandomMultiplier(min, max) {
  return Math.random() * (max - min) + min;
}
async function updatePerformance(req, res) {
  try {
    const { campaign_id } = req.params;

    const campaign = await Campaign.findById(campaign_id);
    if (!campaign) {
      return res.status(404).json({ message: 'Campaign not found' });
    }

    const submissions = await campaignSubmission.find({ campaign_id, content_status: "Approved" }).lean();

    let allResults = [];
    let failedUrls = [];

    for (const submission of submissions) {
      const urls = [...(submission.instagram_urls || []), ...(submission.tiktok_urls || [])];

      for (const url of urls) {
        try {
          // Retry scrape up to 3 times
          const metrics = await scrapeWithRetry(url, 3);

          if (!metrics) {
            throw new Error('No metrics returned');
          }
          
          const engagements = metrics.likes + metrics.comments;
          const rate = getRateForCategory(campaign.category);

          const engagement_rate = metrics.views > 0 ? (engagements / metrics.views) : 0;
          const emv_by_likes = metrics.likes * rate.likes;
          const emv_by_views = metrics.views * rate.views;
          const emv_by_engagement = engagements * rate.engagement;
          const emv_final = Math.max(emv_by_likes, emv_by_views, emv_by_engagement);

          const meta_ads_estimated = engagements * 0.75;
          const adjusted_multiplier = getRandomMultiplier(1.1, 2.0);
          const emv_adjusted = Math.max(emv_final, meta_ads_estimated * adjusted_multiplier);
          const emv_ratio_vs_meta = meta_ads_estimated > 0 ? emv_adjusted / meta_ads_estimated : 0;

          const google_ads_estimated = engagements * 0.90;
         const user = await User.findOne({email: submission.email})
          const data = {
            campaign_id,
            creator_id: user._id,
            content_url: url,
            platform: url.includes('tiktok.com') ? 'TikTok' : 'Instagram',
            views: metrics.views,
            likes: metrics.likes,
            comments: metrics.comments,
            engagement_rate: parseFloat((engagement_rate * 100).toFixed(2)),
            emv_final: parseFloat(emv_adjusted.toFixed(2)),
            google_ads_estimated: parseFloat(google_ads_estimated.toFixed(2)),
            meta_ads_estimated: parseFloat(meta_ads_estimated.toFixed(2)),
            emv_ratio_vs_meta: parseFloat(emv_ratio_vs_meta.toFixed(2)),
            last_updated: metrics.last_updated
          };

          await PerformanceMetrics.findOneAndUpdate(
            { campaign_id, creator_id: submission.user_id, content_url: url },
            data,
            { upsert: true, new: true }
          );

          allResults.push(data);
        } catch (err) {
          console.error(`❌ Failed scraping ${url}:`, err.message);
          failedUrls.push({ url, error: err.message });
        }
      }
    }

    res.json({
      status: 'success',
      scraped_count: allResults.length,
      failed_count: failedUrls.length,
      data: allResults,
      failed: failedUrls
    });
  } catch (err) {
    console.error('❌ Error:', err);
    res.status(500).json({ status: 'failed', message: err.message });
  }
}



async function getCampaignInfo(req, res) {
  try {
    const campaigns = await Campaign.find({ brandId: req.user._id }).lean();

    const result = await Promise.all(
      campaigns.map(async (campaign) => {
        const contents = await PerformanceMetrics.find({ campaign_id: campaign._id }).lean();

        const totalViews = contents.reduce((sum, item) => sum + (item.views || 0), 0);
        const totalLikes = contents.reduce((sum, item) => sum + (item.likes || 0), 0);
        const totalComments = contents.reduce((sum, item) => sum + (item.comments || 0), 0);

        const engagementRates = contents
          .filter(item => item.views > 0)
          .map(item => ((item.likes + item.comments) / item.views) * 100);

        const avgEngagementRate =
          engagementRates.length > 0
            ? engagementRates.reduce((a, b) => a + b, 0) / engagementRates.length
            : 0;

        const emvTotal = contents.reduce((sum, item) => sum + (item.emv_final || 0), 0);
        const googleAdsTotal = contents.reduce((sum, item) => sum + (item.google_ads_estimated || 0), 0);
        const metaAdsTotal = contents.reduce((sum, item) => sum + (item.meta_ads_estimated || 0), 0);

        return {
          id: campaign._id.toString(),
          title: campaign.campaignTitle,
          total_views: totalViews,
          total_likes: totalLikes,
          total_comments: totalComments,
          avg_engagement_rate: parseFloat(avgEngagementRate.toFixed(2)),
          emv_total: emvTotal,
          google_ads_total: googleAdsTotal,
          meta_ads_total: metaAdsTotal,
          has_content: contents.length > 0
        };
      })
    );

    res.json({ campaigns: result });
  } catch (error) {
    console.error("Error fetching campaign data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
}

async function getPerformanceDisplay(req, res) {
  try {
    const { campaign_id } = req.params;

    // Fetch content metrics and submissions for the campaign
    const contents = await PerformanceMetrics.find({ campaign_id }).lean();
    const submissions = await campaignSubmission.find({ campaign_id, content_status :"Approved" }).lean();

    // Extract all unique emails from submissions
    const emailList = submissions.map(sub => sub.email);
    const uniqueEmails = [...new Set(emailList)];

    // Fetch all corresponding users in one query
    const users = await User.find({ email: { $in: uniqueEmails } }).lean();

    // Build a map of email -> user.name
    const userMap = {};
    users.forEach(user => {
      userMap[user._id] = user.name;
    });
    console.log(userMap)
  
    // Build the response using userMap for creator_name lookup
    const result = contents.map(item => ({
      creator_id: item.creator_id,
      creator_name: userMap[item.creator_id] || item.creator_id,  // Fallback if no name found
      content_url: item.content_url,
      platform: item.platform || "Unknown",
      views: item.views || 0,
      likes: item.likes || 0,
      comments: item.comments || 0,
      engagement_rate: item.engagement_rate,
      last_updated: item.last_updated ? item.last_updated.toISOString().split('T')[0] : 'N/A'
    }));

    res.json({ data: result });
  } catch (error) {
    console.error("Error fetching performance data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
}



async function exportCampaignPerformance(req, res) {
  try {
    const { id: campaign_id } = req.params;
    const contents = await PerformanceMetrics.find({ campaign_id }).lean();

    const fields = [
      'Creator Name',
      'Platform',
      'Views',
      'Likes',
      'Comments',
      'Engagement Rate',
      'Last Updated',
      'Content URL'
    ];

    const formattedData = contents.map(item => ({
      'Creator Name': item.creator_id,
      'Platform': item.platform || 'Unknown',
      'Views': item.views || 0,
      'Likes': item.likes || 0,
      'Comments': item.comments || 0,
      'Engagement Rate': `${(((item.likes || 0) + (item.comments || 0)) / (item.views || 1) * 100).toFixed(2)}%`,
      'Last Updated': item.last_updated ? item.last_updated.toISOString().split('T')[0] : 'N/A',
      'Content URL': item.content_url
    }));

    const parser = new Parser({ fields });
    const csv = parser.parse(formattedData);

    res.header('Content-Type', 'text/csv');
    res.attachment(`campaign-performance-${campaign_id}.csv`);
    return res.send(csv);
  } catch (error) {
    console.error('Error exporting CSV:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

module.exports = {
  updatePerformance,
  getCampaignInfo,
  getPerformanceDisplay,
  exportCampaignPerformance
};
