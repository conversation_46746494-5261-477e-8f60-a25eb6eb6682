// controllers/trialController.js
const { BrandSubscription, Brand, BrandPlan } = require('../database');

const activateFreeTrial = async (req, res) => {
  try {
    const { brandId } = req.params;


    // 1. Validate brand
    const brand = await Brand.findById(req.user.id || brandId);
    if (!brand) {
      return res.status(404).json({ message: "Brand not found" });
    }

    // 2. Get Free Trial plan
    const plan = await BrandPlan.findOne({
      isFreeTrial: true,
      isActive: true
    });
    if (!plan) {
      return res.status(404).json({ message: "Free Trial plan not found" });
    }

    // 3. Check if trial ever used (active or expired)
    const previousTrial = await BrandSubscription.findOne({
      brand: brand._id,
      plan: plan._id
    });
    if (previousTrial) {
      return res.status(400).json({  message: "Free Trial already used" });
    }

    // 4. Calculate trial dates
    const now = new Date();
    const trialEnd = new Date(now);
    trialEnd.setMonth(trialEnd.getMonth() + (plan.validityMonths || 1));

    // 5. Create subscription record
    const subscription = await BrandSubscription.create({
      brand: brand._id,
      plan: plan._id,
      campaignsUsed: 0,
      creatorsUsed: 0,
      purchaseDate: now,
      expiresAt: trialEnd,
      status: "active",
      assignedBy: req.user?.id || null
    });

    // 6. Response
    res.status(200).json({
      status: "success", 
      message: "Free Trial activated successfully",
      subscription
    });

  } catch (err) {
    console.error("❌ activateFreeTrial error:", err);
    res.status(500).json({ message: "Server error", error: err.message });
  }
};

module.exports = {
  activateFreeTrial
};
