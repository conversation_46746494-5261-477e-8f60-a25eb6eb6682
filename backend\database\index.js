const mongoose = require("mongoose");
const validator = require("validator");
const dotenv = require("dotenv");
dotenv.config();

const MONGO_URL = process.env.MONGO_URL;

mongoose.connect(MONGO_URL);

const contentAnalysisSchema = new mongoose.Schema({
  url: { type: String, required: true },
  platform: { type: String, enum: ["tiktok", "instagram"], required: true },
  likes: { type: Number, default: 0 },
  comments: { type: Number, default: 0 },
  hashtags: { type: [String], default: [] },
  detectedCategory: { type: [String], default: [] },
  autoTags: { type: [String], default: [] },
  contentStyle: { type: [String], default: [] }, // informative / emotional / review
  productPresence: { type: Boolean, default: false },
  ctaDetected: { type: Boolean, default: false },
  videoLength: { type: Number, default: 0 }, // in seconds
  score: { type: Number, default: 0 }, // content score
}, { _id: false });


const snsSchema = new mongoose.Schema(
  {
    accessToken: { type: String, required: true }, // encrypted before saving
    username: { type: String },
    followerCount: { type: Number, default: 0 },
    postsCount: { type: Number, default: 0 },
    engagementRate: { type: Number, default: 0 }, // optional
  },
  { _id: false }
);
// ✅ Updated User Schema
const UserShema = mongoose.Schema({
  name: {
    type: String,
    required: [true, "Please provide your name"],
    trim: true,
  },
  email: {
    type: String,
    required: [true, "Please provide your email"],
    unique: true,
    lowercase: true,
    validate: [validator.isEmail, "Please provide a valid email"],
  },
  password: {
    type: String,
    minlength: 8,
    // not required for Google users
  },
  country : {
   type : String,
   default : null,
  },
  isGoogleUser: {
    type: Boolean,
    default: false,
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
    welcomeSent: {
    type: Boolean,
    default: false,  // ✅ Added to prevent duplicate welcome emails
  },
  blocked: {
    type: Boolean,
    default: false,
  },
  // ✅ NEW: Paid campaign access status
  paid_status: {
    type: String,
    enum: ["not_eligible", "pending_admin_approval", "approved", "rejected"],
    default: "not_eligible",
  },
  appliedCampaigns: [
    {
      type: [mongoose.Schema.Types.ObjectId],
      ref: "AppliedCampaigns",
    },
  ],
  // ✅ Newly added social media fields
  instagramId: {
    type: String,
    default: "",
    trim: true,
  },
  youtubeId: {
    type: String,
    default: "",
    trim: true,
  },
  tiktokId: {
    type: String,
    default: "",
    trim: true,
  },
  // Update your User schema
points: {
  type: Number,
  default: 0,
},
referrer: {
  type: mongoose.Schema.Types.ObjectId,
  ref: "User",
  default: null,
},
referrals: [
  {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    default: [],
  },
  
],

    // ✅ New: URL submission requirement
    submittedUrls: {
      type: [String],
      default: [],
    },
    urlsSubmitted: {
      type: Boolean,
      default: false, // becomes true only after submitting 3 valid URLs
    },
 
    // SNS OAuth integrations
  sns: {
    instagram: { type: snsSchema, default: null },
    tiktok: { type: snsSchema, default: null },
  },
  snsConnected: { type: Boolean, default: false },

  // Content analysis results
  contentAnalysis: { type: [contentAnalysisSchema], default: [] },

  // AI Recommendation metadata
  predictedCategories: { type: [String], default: [] },
  creatorTags: { type: [String], default: [] },
  recommendationScore: { type: Number, default: 0 },
  whyRecommended: { type: [String], default: [] },

  createdAt: {
    type: Date,
    default: Date.now,
  },
});




const SocialAuthSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: "User", index: true, unique: true },
  instagram: {
    connected: { type: Boolean, default: false },
    access: { type: String, default: null }, // encrypted blob
    refresh: { type: String, default: null }, // encrypted blob
    username: String,
    followerCount: Number,
    lastSyncAt: Date,
  },
  tiktok: {
    connected: { type: Boolean, default: false },
    access: { type: String, default: null },
    refresh: { type: String, default: null },
    username: String,
    followerCount: Number,
    lastSyncAt: Date,
  },
});


const invitationSchema = new mongoose.Schema(
  {
    campaign: { type: mongoose.Schema.Types.ObjectId, ref: "Campaign", required: true },
    creator: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
    status: {
      type: String,
      enum: ["pending", "accepted", "declined", "expired"],
      default: "pending",
    },
    expiresAt: { type: Date }, // from campaign deadline
  },
  { timestamps: true }
);

// Auto-expire invitations (check on query)
invitationSchema.pre("find", function () {
  this.where({
    $or: [{ expiresAt: null }, { expiresAt: { $gte: new Date() } }],
  });
});



// Updated Campaign Schema
const CampaignSchema = new mongoose.Schema({
  // Basic Info
  campaignType: {
    type: String,
    enum: ['gifted', 'paid'],
    default: 'gifted',
  },
  campaignTitle: {
    type: String,
    required: [true, 'Campaign title is required'],
    trim: true,
  },
  campaignIndustry: {
    type: String,
    required: [true, 'Campaign industry is required'],
    trim: true,
  },
  category: {
    type: String,
    enum: ['Beauty', 'Food', 'Beverage', 'Wellness & Supplements', 'Personal Care'],
    required: [true, 'Category is required'],
  },
  brandName: {
    type: String,
    required: [true, 'Brand name is required'],
    trim: true,
  },
  brandId: {
   type: mongoose.Schema.Types.ObjectId,
   ref: "Brand",
  },

  // Product Info
  productName: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
  },
  productDescription: {
    type: String,
    required: [true, 'Product description is required'],
    trim: true,
  },

  // Images
  brandLogo: {
    type: String,
    default: '',
  },
  productImages: {
    type: [String],
    validate: [
      function (arr) { return arr.length <= 4; },
      'Up to 4 product images are allowed',
    ],
    default: [],
  },

  // Creator & Reach
  creatorCount: {
    type: Number,
    required: [true, 'Creator count is required'],
    //enum: [5, 10, 15, 20],
    default: 5,
  },
  recruiting: {
    type: Number,
    default: 0,
  },
  influencersReceive: {
    type: String,
   // required: [true, 'Offer details are required'],
  },

  // Content & Tracking
  contentFormat: {
    type: [String],
    required: [true, 'Content format is required'],
  },
  requiredHashtags: {
    type: [String],
    default: [],
  },
  mentionHandle: {
    type: String,
    default: '',
    trim: true,
  },
  toneGuide: {
    type: [String],
    default: '',
  },
  referenceContent: {
    type: [String], // URLs or file paths
    default: [],
  },

  // Deadlines
  deadline: {
    type: Date,
    required: [true, 'Deadline is required'],
    validate: [validator.isDate, 'Invalid date format'],
  },
  recruitmentEndDate: {
    type: Date,
    required: [true, 'Recruitment end date is required'],
    validate: [validator.isDate, 'Invalid recruitment end date'],
  },

  // Participation Requirements
  participationRequirements: {
    type: String,
   // required: [true, 'Participation requirements are required'],
  },

  // Category-Specific optional fields
  beautyDetails: {
    productType: String,
    skinTypes: [String],
    keyIngredients: [String],
    usageInstructions: String,
  },
  foodDetails: {
    preparationMethod: String,
    dietaryTags: [String],
    eatingScene: String,
  },
  beverageDetails: {
    servingType: String,
    servingTemperature: String,
    caffeineContent: String,
    dietaryTags: [String],
  },
  wellnessDetails: {
    productType: String,
    targetFunctions: [String],
    formType: String,
    usageTiming: String,
    flavor: String,
    dietaryTags: [String],
  },
  personalCareDetails: {
    productType: String,
    useAreas: [String],
    keyIngredients: [String],
    scent: String,
    texture: String,
  },

  // Status & meta
  status: {
    type: String,
    enum: ['Pending', 'Active', 'Deactive', 'Completed'],
    default: 'Active',
  },
  referenceId: {
  type: mongoose.Schema.Types.ObjectId,
  ref: 'BrandCampaignRequest'
},

// Paid Campaign fields
pricingModel: {
  type: String,
  enum: ['fixed', 'bidding'],
  required: function() { return this.campaignType === 'paid'; }
},
fixedPrice: {
  type: Number,
  min: 5
},
minBid: {
  type: Number,
  min: 1
},
maxBid: {
  type: Number,
  min: 1
},
requestedContent: {
  videos: { type: Number, min: 0, default: 0 },
  photos: { type: Number, min: 0, default: 0 },
  notes: { type: String, default: '' }
},
creatorRequirements: {
  followerCount: { type: String, default: 'Any' },
  location: { type: String, default: '' },
  minAge: { type: String, default: '' },
  maxAge: { type: String, default: '' },
  gender: { type: String, default: 'Any' }
},
usageTerms: {
  usageRightsDuration: { type: String, default: '' },
  lateSubmissionPenalty: { type: String, default: '' },
  paymentResponsibilityNotice: { type: Boolean, default: false },
  usageRights: { type: [String], default: [] },
  usageRightsOther: { type: String, default: '' }
},

}, { timestamps: true });


const appliedCampaignsSchema = mongoose.Schema({
  name: {
    type: String,
    required: [true, "Please provide your name"],
    trim: true,
  },
  email: {
    type: String,
    required: [true, "Please provide your email"],
    lowercase: true,
    validate: [validator.isEmail, "Please provide a valid email"],
  },
  phone: {
    type: String,
    required: [true, "Please enter your phone number"],
    trim: true,
  },
  address: {
    type: String,
    required: [true, "Please enter your address"],
    trim: true,
  },
  city: {
    type: String,
    required: [true, "Please provide your city"],
    trim: true,
  },
  state: {
    type: String,
    required: [true, "Please select a state or province"],
    trim: true,
    uppercase: true,
  },
  country: {
    type: String,
    default: null
  },
  zipCode: {
    type: String,
    default : null
  },

  // ✅ Social fields
  instagramId: {
    type: String,
    default: "",
    trim: true,
  },
  tiktokId: {
    type: String,
    default: "",
    trim: true,
  },

  // ✅ Bid field for paid bidding campaigns
  bid: {
    type: Number,
    min: 1,
    default: null,
  },

  // ✅ Application status
  status: {
    type: String,
    enum: ["Pending", "Approved", "Rejected","withdrawn"],
    default: "Pending",
  },
  rejectionReason: {
    type: String,
    default: "",
  },
  showReasonToInfluencer: {
    type: Boolean,
    default: false,
  },

  // ✅ Submission fields
  postUrl: {
    type: String,
    default: "",
  },
  allowReuse: {
    type: Boolean,
    default: false,
  },

  // ✅ Timestamps and references
  createdAt: {
    type: Date,
    default: Date.now,
  },
  campaign: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Campaigns",
  },
});

const blockedCreatorSchema = new mongoose.Schema({
  brandId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Brand',
    required: true,
  },
  creatorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  blockedAt: {
    type: Date,
    default: Date.now,
  },
});

// Prevent duplicate blocks for same brand + creator pair
blockedCreatorSchema.index({ brandId: 1, creatorId: 1 }, { unique: true });

const BlockedCreator = mongoose.model('BlockedCreator', blockedCreatorSchema);

const campaignSubmissionSchema = new mongoose.Schema(
  {
    campaign_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Campaign',
      required: true,
    },
    user_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    email: {
      type: String,
      required: true,
      lowercase: true,
      validate: [validator.isEmail, 'Please provide a valid email'],
    },
    instagram_urls: {
      type: [String],
      default: [],
    },
    tiktok_urls: {
      type: [String],
      default: [],
    },
    allow_brand_reuse: {
      type: Boolean,
      default: false,
    
    },
    status: {
      type: String,
      enum: ['submitted', 'reviewed', 'rejected'],
      default: 'submitted',
    },
    // ✅ Content review status (visible to brand/creator)
    content_status: {
      type: String,
      enum: ['Pending', 'Approved', 'Rejected'],
      default: 'Pending',
    },
   reminder_status: {
  type: Object,
  default: {}
},

    // ✅ NEW Tracking Info Section
   tracking_info: {
  courier: {
    type: String,
    default: "", // e.g. UPS, FedEx, USPS
  },
  tracking_number: {
    type: String,
    maxlength: 50,
    default: "",
  },
  tracking_link: {
    type: String,
    default: "", // Generated 17track.net URL
  },
  last_updated: {
    type: Date,
    default: null,
  },
  
  delivery_status: {
    type: String,
    default: "❌ Not Found", // Used for frontend emoji display
  },
  origin_info: {
    type: Object,
    default: {}, // Raw 17TRACK origin info (tracking history, country, timestamps)
  }
}

  },
  {
    timestamps: {
      createdAt: 'submitted_at',
      updatedAt: 'updated_at',
    },
  }
);


const requestLogSchema = mongoose.Schema({
  email: {
    type: String,
    default: "N/A",
    lowercase: true,
  },
  ip: {
    type: String,
    required: true,
  },
  userAgent: {
    type: String,
    required: true,
  },
  action: {
    type: String,
    enum: ['signup', 'signin', 'google-auth', 'apply-campaign', 'forgot-password'],
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

const referralRewardSchema = new mongoose.Schema(
  {
    referrer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: false, // ✅ optional now
    },
    invitee: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    rewardGiven: {
      type: Boolean,
      default: false,
    },
    approvedAt: {
      type: Date,
      default: null,
    },
    revoked: {
      type: Boolean,
      default: false,
    }
  },
  {
    timestamps: true, // ✅ createdAt and updatedAt automatically managed
  }
);

module.exports = mongoose.model("ReferralReward", referralRewardSchema);


const pointEventLogSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  source: {
    type: String,
    enum: [
  'content-approval',
  'first-content',
  'referral-bonus',
  'manual',
  'revoke',
  'content-approved',
  'redeem', // ✅ ADD THIS
],
    required: true
  },
  points: { type: Number, required: true }, // +ve / -ve
  note: { type: String, default: '' },
  relatedUser: { type: mongoose.Schema.Types.ObjectId, ref: "User", default: null },
  campaign: { type: mongoose.Schema.Types.ObjectId, ref: "Campaign", default: null },
  createdAt: { type: Date, default: Date.now },
});


const rewardTierSchema = new mongoose.Schema({
  pointsRequired: { type: Number, required: true },
  rewardLabel: { type: String, required: true }, // e.g. "$25 Amazon Gift Card"
  rewardValue: { type: Number, required: true }, // e.g. 25
  active: { type: Boolean, default: true }
});

const rewardTransactionSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  rewardTier: { type: mongoose.Schema.Types.ObjectId, ref: "RewardTier", required: true },
  status: {
    type: String,
    enum: ['pending', 'completed'],
    default: 'pending'
  },
  createdAt: { type: Date, default: Date.now },
  adminNote: { type: String, default: '' }
});

const pointRuleSchema = new mongoose.Schema({
  key: { type: String, required: true, unique: true }, // e.g. "referral_bonus"
  value: { type: Number, required: true },
});


const brandSchema = new mongoose.Schema({
  companyName:    { type: String, required: true },
  contactName:    { type: String, required: true },
  email:          { type: String, required: true, unique: true },
  phone:          { type: String },
  website:        { type: String },
  productCategory:{ type: String },
  logo:           { type: String },        // ← URL or path to uploaded logo
  introduction:   { type: String },        // ← your "Brand Introduction"
  password:       { type: String },   
  isVerified:     { type: Boolean, default: false },
}, { timestamps: true });


const brandPlanSchema = new mongoose.Schema({
  name: { type: String, required: true },
  planType: { type: String, enum: ['one-time', 'subscription'], required: true },
  price: { type: String, required: true },
  currency: { type: String, default: '$' }, // Default to USD
  campaignsAllowed: { type: Number, required: true },
  creatorsAllowed: { type: Number, required: true },
  creatorsPerCampaign: { type: Number }, // Only for subscription
  validityMonths: { type: Number }, // Only for one-time plans
  autoResetMonthly: { type: Boolean, default: false }, // Subscription plans
  targetAudience: { type: String, required: true },
  features: [{ type: String }],
  supportLevel: { type: String },
  analyticsLevel: { type: String },
  competitorReport: { type: Boolean, default: false },
  bestFor: { type: String },
  isPopular: { type: Boolean, default: false },
  isBestValue: { type: Boolean, default: false }
},{
  timestamps: true,
});


const brandSubscriptionSchema = new mongoose.Schema({
  brand:           { type: mongoose.Schema.Types.ObjectId, ref: "Brand", required: true },
  plan:            { type: mongoose.Schema.Types.ObjectId, ref: "BrandPlan", required: true },
  campaignsUsed:   { type: Number, default: 0 },
  creatorsUsed:    { type: Number, default: 0 },
  purchaseDate:    { type: Date, default: Date.now },
  expiresAt:       { type: Date },
  upgradedFrom:    { type: mongoose.Schema.Types.ObjectId, ref: "BrandPlan" },
  stripeSessionId: { type: String },
  status:          { type: String, default: "active" },
  upgradeEligibleTill: { type: Date },
  // New fields for admin management
  extraQuota:      { type: Number, default: 0 },  // Extra recruit quota for gifted campaigns
  extraCampaignLimit: { type: Number, default: 0 }, // Extra campaign limit for paid campaigns
  extraCreatorLimit:  { type: Number, default: 0 }, // Extra creator limit for paid campaigns

  // ─── NEW FIELDS FOR ADD-ONS ───────────────────────────────
  extraCampaignsAllowed: { type: Number, default: 0 },
  extraCreatorsAllowed:  { type: Number, default: 0 },
});



const paymentHistorySchema = new mongoose.Schema({
  brand: { type: mongoose.Schema.Types.ObjectId, ref: "Brand", required: true },
  plan: { type: mongoose.Schema.Types.ObjectId, ref: "BrandPlan", required: true },
  type: { type: String, enum: ["purchase", "upgrade", "addon"], required: true },
  amountPaid: { type: Number, required: true },
  stripeSessionId: { type: String, required: true },
  metadata: { type: Object },                         // upgrade_from, upgrade_to, etc.
  createdAt: { type: Date, default: Date.now }
});

const brandPlanHistorySchema = new mongoose.Schema({
  brand: { type: mongoose.Schema.Types.ObjectId, ref: "Brand", required: true },
  oldPlan: { type: String },
  newPlan: { type: String, required: true },
  oldPlanType: { type: String},
  newPlanType: { type: String, required: true },
  oldValidityStart: { type: Date },
  oldValidityEnd: { type: Date },
  newValidityStart: { type: Date },
  newValidityEnd: { type: Date },
  oldExtraQuota: { type: Number, default: 0 },
  newExtraQuota: { type: Number, default: 0 },
  oldExtraCampaignLimit: { type: Number, default: 0 },
  newExtraCampaignLimit: { type: Number, default: 0 },
  oldExtraCreatorLimit: { type: Number, default: 0 },
  newExtraCreatorLimit: { type: Number, default: 0 },
  changedBy: { type: String, required: true },
  changeDate: { type: Date, default: Date.now }
}, { timestamps: true });

const shipmentTrackingSchema = new mongoose.Schema({
  brand: { type: mongoose.Schema.Types.ObjectId, ref: "Brand", required: true },
  creator: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  campaign: { type: mongoose.Schema.Types.ObjectId, ref: "Campaign", required: true },
  carrier: { type: String, required: true },              // UPS, FedEx, etc.
  trackingNumber: { type: String, required: true },
  sentOn: { type: Date, default: Date.now }
});

const BrandCampaignRequestSchema = new mongoose.Schema({
  brand: { type: mongoose.Schema.Types.ObjectId, ref: 'Brand', required: true },
  
  // Campaign type - can be gifted or paid
  campaignType: { 
    type: String, 
    enum: ['gifted', 'paid'], 
    required: true,
    default: 'gifted'
  },
  
  // Mirror all fields from CampaignSchema
  campaignTitle: String,
  campaignIndustry: String,
  category: String,
  brandName: String,
  productName: String,
  productDescription: String,
  brandLogo: String,
  productImages: [String],
  creatorCount: Number,
  recruiting: Number,
  influencersReceive: String,
  contentFormat: [String],
  requiredHashtags: [String],
  mentionHandle: String,
  toneGuide: [String],
  referenceContent: [String],
  deadline: Date,
  recruitmentEndDate: Date,
  participationRequirements: String,
  
  beautyDetails: mongoose.Schema.Types.Mixed,
  foodDetails: mongoose.Schema.Types.Mixed,
  beverageDetails: mongoose.Schema.Types.Mixed,
  wellnessDetails: mongoose.Schema.Types.Mixed,
  personalCareDetails: mongoose.Schema.Types.Mixed,
  
  // Approval Workflow
  status: { type: String, enum: ['Pending','Pending Review','Approved','Rejected'], default: 'Pending' },
  approvalStatus: { type: String, enum: ['Pending', 'Approved', 'Rejected'], default: 'Pending' },

  // Paid Campaign fields
  pricingModel: {
    type: String,
    enum: ['fixed', 'bidding'],
    required: function() { return this.campaignType === 'paid'; }
  },
  rejection_reason: {
    type: String,
    default: '',
    maxlength: 200
  },
  fixedPrice: {
    type: Number,
    min: 5,
  },
  minBid: {
    type: Number,
    min: 1
  },
  maxBid: {
    type: Number,
    min: 1
  },
  requestedContent: {
    videos: { type: Number, min: 0, default: 0 },
    photos: { type: Number, min: 0, default: 0 },
    notes: { type: String, default: '' }
  },
  creatorRequirements: {
    followerCount: { type: String, default: 'Any' },
    location: { type: String, default: '' },
    minAge: { type: String, default: '' },
    maxAge: { type: String, default: '' },
    gender: { type: String, default: 'Any' }
  },
  usageTerms: {
    usageRightsDuration: { type: String, default: '' },
    lateSubmissionPenalty: { type: String, default: '' },
    paymentResponsibilityNotice: { type: Boolean, default: false },
    usageRights: { type: [String], default: [] },
    usageRightsOther: { type: String, default: '' }
  },

}, { timestamps: true });


const PerformanceSchema = new mongoose.Schema({
  campaign_id: { type: String, required: true, ref: "Campaigns" },
  creator_id: { type: String, required: true, ref : "User" },
  content_url: { type: String, required: true},
  platform: { type: String, enum: ["TikTok", "Instagram"], required: false },
  views: { type: Number, default: 0 },
  likes: { type: Number, default: 0 },
  comments: { type: Number, default: 0 },
  engagement_rate: { type: Number, default: 0.0 },
  emv_final: { type: Number, default: 0 },
  google_ads_estimated: { type: Number, default: 0 },
  meta_ads_estimated: { type: Number, default: 0 },
  emv_ratio_vs_meta: { type: Number, default: 0.0 },
  last_updated: { type: Date, default: Date.now }
});

PerformanceSchema.index({ campaign_id: 1, creator_id: 1, content_url: 1 }, { unique: true });

const PerformanceMetrics = mongoose.model("PerformanceMetrics", PerformanceSchema);



const RequestLog = mongoose.model("RequestLog", requestLogSchema);
const User = mongoose.model("User", UserShema);
const SocialAuth = mongoose.model("SocialAuth", SocialAuthSchema);
const Campaign = mongoose.model("Campaigns", CampaignSchema);
const appliedCampaigns = mongoose.model("AppliedCampaigns", appliedCampaignsSchema);
const campaignSubmission = mongoose.model("CampaignSubmission", campaignSubmissionSchema);
const referralReward = mongoose.model("ReferralReward", referralRewardSchema);
const pointEventLog = mongoose.model("PointEventLog", pointEventLogSchema);
const rewardTier = mongoose.model("RewardTier", rewardTierSchema);
const rewardTransaction = mongoose.model("RewardTransaction", rewardTransactionSchema);
const pointRule = mongoose.model("PointRule", pointRuleSchema);

const Brand = mongoose.model("Brand", brandSchema);
const BrandPlan = mongoose.model("BrandPlan", brandPlanSchema);
const BrandSubscription = mongoose.model("BrandSubscription", brandSubscriptionSchema);
const BrandPlanHistory = mongoose.model("BrandPlanHistory", brandPlanHistorySchema);
const PaymentHistory = mongoose.model("PaymentHistory", paymentHistorySchema);
const ShipmentTracking = mongoose.model("ShipmentTracking", shipmentTrackingSchema);

const BrandCampaignRequest = mongoose.model("BrandCampaignRequest", BrandCampaignRequestSchema);

// ✅ NEW: Creator Access Request Schema
const creatorAccessRequestSchema = new mongoose.Schema({
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  status: {
    type: String,
    enum: ["pending", "approved", "rejected"],
    default: "pending",
  },
  platform: {
    type: String,
    enum: ["TikTok", "Instagram"],
    required: true,
  },
  socialHandle: {
    type: String,
    required: true,
    trim: true,
  },
  socialLink: {
    type: String,
    required: true,
  },
  giftedCampaignsSubmitted: {
    type: Number,
    default: 0,
  },
  followerCount: {
    type: Number,
    default: null,
  },
  rejectionReason: {
    type: String,
    default: "",
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Admin",
    default: null,
  },
  approvedAt: {
    type: Date,
    default: null,
  },
  rejectedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Admin",
    default: null,
  },
  rejectedAt: {
    type: Date,
    default: null,
  },
}, { timestamps: true });

const CreatorAccessRequest = mongoose.model("CreatorAccessRequest", creatorAccessRequestSchema);

// ✅ NEW: Extension Request Schema
const extensionRequestSchema = new mongoose.Schema({
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  campaign: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Campaigns',
    required: true,
  },
  daysRequested: {
    type: Number,
    min: 1,
    max: 5,
    required: true,
  },
  reason: {
    type: String,
    required: true,
    maxlength: 300,
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending',
  },
  decisionBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Brand',
    default: null,
  },
  decisionAt: {
    type: Date,
    default: null,
  },
}, { timestamps: true });

const ExtensionRequest = mongoose.model('ExtensionRequest', extensionRequestSchema);

const Invitation = mongoose.model("Invitation", invitationSchema);



module.exports = {
  User,
  SocialAuth,
  Invitation,
  Campaign,
  appliedCampaigns,
  campaignSubmission,
  RequestLog,
  referralReward,
  pointEventLog,
  rewardTier,
  rewardTransaction,
  pointRule,

   // ✅ New Brand System
  Brand,
  BrandPlan,
  BrandSubscription,
  BrandPlanHistory,
  PaymentHistory,
  ShipmentTracking,

  BrandCampaignRequest,
  CreatorAccessRequest, // ✅ NEW
  ExtensionRequest, // ✅ NEW
  PerformanceMetrics,
  BlockedCreator
};
