const cron = require('node-cron');
const dayjs = require('dayjs');
const { campaignSubmission, Campaign, User } = require('../database');
const sendEmail = require('../utils/sendEmail');


// Live: run daily at 9:00 AM local time
cron.schedule('0 9 * * *', async () => {
  console.log('⏰ Running content-submission-reminder-cron');

  try {
    const submissions = await campaignSubmission.find({
      'tracking_info.delivery_status': 'Delivered',
      content_status: 'Pending',
      $or: [
        { instagram_urls: { $exists: false } },
        { instagram_urls: { $size: 0 } },
        { tiktok_urls: { $exists: false } },
        { tiktok_urls: { $size: 0 } }
      ]
    });

    for (const submission of submissions) {
      processReminder(submission)
    }

    console.log(`📨 Queued ${submissions.length} reminders`);
  } catch (err) {
    console.error('❌ Error fetching submissions:', err);
  }
});

async function processReminder(submission) {
  try {
    const deliveredAt = submission.tracking_info.last_updated;
    if (!deliveredAt) {
      console.warn(`⚠️ No delivery date for submission ${submission._id}`);
      return;
    }

    const today = dayjs();
    const deliveryDate = dayjs(deliveredAt);
    const deadlineDate = deliveryDate.add(10, 'day');
    const daysSinceDelivery = today.diff(deliveryDate, 'day');
    const daysUntilDeadline = deadlineDate.diff(today, 'day');

    console.log(`📆 Submission ${submission.email}: ${daysSinceDelivery} days since delivery, ${daysUntilDeadline} days until deadline`);

    let reminderType = null;
    let templateName = '';
    let subject = '';

    if (daysSinceDelivery === 0 && !submission.reminder_status?.d10) {
      reminderType = 'd10';
      templateName = 'reminder_d10';
      subject = 'Your content is due in 10 days';
    } else if (daysSinceDelivery === 3 && !submission.reminder_status?.d7) {
      reminderType = 'd7';
      templateName = 'reminder_d7';
      subject = '1 week left to submit your content';
    } else if (daysSinceDelivery === 6 && !submission.reminder_status?.d4) {
      reminderType = 'd4';
      templateName = 'reminder_d4';
      subject = 'Don’t forget to submit your content';
    } else if (daysUntilDeadline === 1 && !submission.reminder_status?.d1) {
      reminderType = 'd1';
      templateName = 'reminder_d1';
      subject = 'Final reminder: Content due tomorrow';
    } else if (daysSinceDelivery === 10 && !submission.reminder_status?.dday) {
      reminderType = 'dday';
      templateName = 'reminder_dday';
      subject = 'Today is the deadline!';
    } else {
      console.log(`ℹ️ No reminder needed for submission ${submission._id}`);
      return;
    }

    const [campaign, user] = await Promise.all([
      Campaign.findById(submission.campaign_id),
      User.findOne({ email: submission.email })
    ]);

    if (!campaign || !user) {
      console.warn(`⚠️ Missing campaign/user for submission ${submission._id}`);
      return;
    }

    await sendEmail({
      userName: user.name,
      campaignTitle: campaign.campaignTitle,
      templateName,
      subject,
      to: user.email
    });

    if (!submission.reminder_status) {
      submission.reminder_status = {};
    }

    submission.reminder_status[reminderType] = true;
    await submission.save();

    console.log(`✅ Sent ${reminderType} reminder to ${user.email}`);
  } catch (err) {
    console.error(`❌ Error processing submission ${submission._id}:`, err);
  }
}
