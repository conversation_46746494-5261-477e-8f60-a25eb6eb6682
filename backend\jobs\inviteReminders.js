// jobs/inviteReminders.js
const dayjs = require("dayjs");
const {Campaign,Invitation} = require("../database");

// TODO: plug your email transport
async function sendEmail(to, subject, text) {
  console.log(`[EMAIL] to=${to} subject=${subject} text=${text}`);
}

async function runCycle() {
  const now = dayjs();

  // 1) 48h after invite → 1st reminder (max twice total)
  const remind48 = await Invitation.find({
    status: "pending",
    remindersSent: { $lt: 1 },
    createdAt: { $lte: now.subtract(48, "hour").toDate() },
  }).populate("campaign creator", "deadline email name");

  for (const inv of remind48) {
    // send to creator
    await sendEmail(inv.creator.email, "Reminder: You have a Matchably invite", `Please respond to campaign "${inv.campaign.name}".`);
    inv.remindersSent = 1;
    await inv.save();
  }

  // 2) 24h before deadline → 2nd reminder
  const soon = await Invitation.find({
    status: "pending",
    remindersSent: { $lt: 2 },
  }).populate("campaign creator", "deadline email name");

  for (const inv of soon) {
    if (!inv.campaign?.deadline) continue;
    const hrsLeft = dayjs(inv.campaign.deadline).diff(now, "hour");
    if (hrsLeft <= 24 && inv.remindersSent < 2) {
      await sendEmail(inv.creator.email, "Reminder: Invitation expiring", `Campaign "${inv.campaign.name}" ends soon.`);
      inv.remindersSent = 2;
      await inv.save();
    }
  }

  // 3) Deadline - 24h: Notify both brand & creator (one-time)
  const campaignsSoon = await Campaign.find({
    deadline: { $gte: now.toDate(), $lte: now.add(24, "hour").toDate() },
  }).populate("brand", "email name");
  for (const c of campaignsSoon) {
    await sendEmail(c.brand.email, "Heads up: Campaign deadline in 24h", `Campaign "${c.name}" ends in 24 hours.`);
    // creators still pending
    const pendings = await Invitation.find({ campaign: c._id, status: "pending" }).populate("creator","email name");
    for (const p of pendings) {
      await sendEmail(p.creator.email, "Heads up: Campaign deadline in 24h", `Invitation for "${c.name}" ends in 24 hours.`);
    }
  }

  // 4) At campaign close: auto-decline pending
  const closed = await Campaign.find({ closesAt: { $lte: now.toDate() } });
  for (const c of closed) {
    const pendings = await Invitation.find({ campaign: c._id, status: "pending" }).populate("creator brand");
    for (const p of pendings) {
      p.status = "expired";
      await p.save();
      // notify both
      await sendEmail(p.creator.email, "Invitation closed", `Invitation for "${c.name}" is now closed.`);
    }
  }
}

function startInviteReminderJob() {
  // every 10 minutes
  setInterval(runCycle, 10 * 60 * 1000);
  console.log("[jobs] inviteReminders started (10m interval).");
}

module.exports = { startInviteReminderJob };
