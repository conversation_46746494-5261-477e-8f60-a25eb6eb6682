const cron = require('node-cron');
const { Campaign } = require('../database');
const { updatePerformance } = require('../controllers/performanceController');

/// cronJob.js == every hour
cron.schedule('0 * * * *', async () => {
  console.log("Running daily scrape...");

  const campaigns = await Campaign.find({ status: 'Active' });
  for (const campaign of campaigns) {
    try {
      await updatePerformance({ params: { campaign_id: campaign._id } }, { json: () => {} });
      console.log(`Scraped campaign: ${campaign.campaignTitle}`);
    } catch (err) {
      console.error(`Failed for ${campaign.campaignTitle}`, err.message);
    }
  }
});
