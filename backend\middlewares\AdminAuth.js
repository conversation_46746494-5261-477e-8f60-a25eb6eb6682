/** @format */

const dotenv = require("dotenv");
const bcrypt = require("bcrypt");
const { generateToken, verifyToken } = require("../utils/jwtUtils");

dotenv.config();

const ADMIN_USER = "<EMAIL>";
const ADMIN_HASHED_PASS = "$2b$10$kthSu.4WHdClXAhEdcEbTeChxrfKC0hqidI3sDJv3uDTTa56KjbR2";

// ✅ Login Function
async function Login(req, res) {
	const { username, password } = req.body;

	if (username !== ADMIN_USER) {
		return res.json({
			status: "failed",
			message: "Username or password incorrect",
		});
	}

	const isMatch = await bcrypt.compare(password, ADMIN_HASHED_PASS);
	if (!isMatch) {
		return res.json({
			status: "failed",
			message: "Username or password incorrect",
		});
	}

	const token = generateToken({ username, role: "admin" , id : "608c3ae611318"});

	res.json({
		status: "success",
		message: "Login successful",
		token,
	});
}

// ✅ Middleware to verify token for protected admin routes
async function VerifyTokenAuth(req, res, next) {
  const token = req.headers.authorization;

  try {
    const decoded = await verifyToken(token);

    // console.log("🔐 Decoded Token:", decoded); // helpful for debugging

    if (!decoded || decoded.role !== "admin" || decoded.username !== process.env.ADMIN_USER) {
      return res.status(403).json({ status: "failed", message: "Unauthorized" });
    }

    next(); // Token valid
  } catch (err) {
    console.error("❌ Token verification failed:", err.message || err);
    return res.status(401).json({ status: "failed", message: "Invalid or expired token" });
  }
}


// ✅ Used for /admin/verify route
async function VerifyToken(req, res) {
	const token = req.headers.authorization;

	try {
		const decoded = await verifyToken(token);
		console.log(decoded);
console.log(decoded.role !== "admin")
		if (decoded.role !== "admin") {
			return res.json({ status: "failed", message: "Unauthorized" });
		}

		res.json({
			status: "success",
			message: "Verified",
		});
	} catch {
		res.json({ status: "failed", message: "Invalid or expired token" });
	}
}

async function VerifyTokenController(req, res, next) {
	const token = req.headers.authorization;

	try {
		const decoded = await verifyToken(token);
		if (decoded.role !== "admin") {
			return res.status(403).json({ status: "failed", message: "Unauthorized" });
		}
		req.user = decoded; // Attach user info to request
		next(); // Pass control to the next middleware/route handler
	} catch {
		res.status(401).json({ status: "failed", message: "Invalid or expired token" });
	}
}

module.exports = {
	Login,
	VerifyToken,
	VerifyTokenAuth,
	VerifyTokenController
};
