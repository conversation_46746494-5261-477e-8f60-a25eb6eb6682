const { Brand, BrandPlan, BrandSubscription, BrandPlanHistory } = require('../../database');

/**
 * GET /api/admin/brands/plans
 * Get all brands with their current plan information
 */
const getBrandsWithPlans = async (req, res) => {
  try {
    // Get all verified brands
    const brands = await Brand.find({}).select('companyName email');
    //console.log('Found brands:', brands.length);
    
    const brandsWithPlans = await Promise.all(
      brands.map(async (brand) => {
        // Get current subscription
        const subscription = await BrandSubscription
          .findOne({ brand: brand._id })
          .populate('plan')
          .sort({ _id: -1 });

        // Get plan history count
        const historyCount = await BrandPlanHistory.countDocuments({ brand: brand._id });

        return {
          id: brand._id,
          name: brand.companyName,
          email: brand.email,
          currentPlan: subscription?.plan?.name || null,
          planType: subscription?.plan?.type || null,
          validityStart: subscription?.purchaseDate || null,
          validityEnd: subscription?.expiresAt || null,
          extraQuota: subscription?.extraQuota || 0,
          campaignLimit: subscription?.extraCampaignLimit || 0,
          creatorLimit: subscription?.extraCreatorLimit || 0,
          defaultCampaignLimit: subscription?.plan?.campaignsAllowed || 0,
          defaultCreatorLimit: subscription?.plan?.creatorsAllowed || 0,
          hasHistory: historyCount > 0
        };
      })
    );

    res.json({
      status: 'success',
      brands: brandsWithPlans
    });
  } catch (error) {
    console.error('Error fetching brands with plans:', error);
    res.status(500).json({
      status: 'failed',
      message: 'Failed to fetch brands with plans'
    });
  }
};

/**
 * POST /api/admin/brands/:brandId/plan
 * Update a brand's plan and settings
 */
const updateBrandPlan = async (req, res) => {
  try {
    const { brandId } = req.params;
    const {
      planType,
      plan,
      validityStart,
      validityEnd,
      extraQuota,
      campaignLimit,
      creatorLimit
    } = req.body;

    // Find the brand
    const brand = await Brand.findById(brandId);
    if (!brand) {
      return res.status(404).json({
        status: 'failed',
        message: 'Brand not found'
      });
    }
    console.log(plan, planType)

    // Find the plan by name and type
    const brandPlan = await BrandPlan.findOne({ name: plan, planType: planType });
    if (!brandPlan) {
      return res.status(404).json({
        status: 'failed',
        message: `Plan not found: ${plan} with type ${planType}`
      });
    }

    // Get current subscription for history
    const currentSubscription = await BrandSubscription
      .findOne({ brand: brandId, status: 'active' })
      .populate('plan');

    // Create history entry
    if (currentSubscription) {
      await BrandPlanHistory.create({
        brand: brandId,
        oldPlan: currentSubscription.plan?.name,
        newPlan: plan,
        oldPlanType: currentSubscription.plan?.type,
        newPlanType: planType,
        oldValidityStart: currentSubscription.purchaseDate,
        oldValidityEnd: currentSubscription.expiresAt,
        newValidityStart: validityStart,
        newValidityEnd: validityEnd,
        oldExtraQuota: currentSubscription.extraQuota || 0,
        newExtraQuota: extraQuota || 0,
        oldExtraCampaignLimit: currentSubscription.extraCampaignLimit || 0,
        newExtraCampaignLimit: campaignLimit || 0,
        oldExtraCreatorLimit: currentSubscription.extraCreatorLimit || 0,
        newExtraCreatorLimit: creatorLimit || 0,
        changedBy: req.user?.email || 'Admin',
        changeDate: new Date()
      });

      // Deactivate current subscription
      currentSubscription.status = 'inactive';
      await currentSubscription.save();
    }

    // Create new subscription
    const newSubscription = await BrandSubscription.create({
      brand: brandId,
      plan: brandPlan._id,
      purchaseDate: validityStart || new Date(),
      expiresAt: validityEnd,
      extraQuota: extraQuota || 0,
      extraCampaignLimit: campaignLimit || 0,
      extraCreatorLimit: creatorLimit || 0,
      status: 'active'
    });

    res.json({
      status: 'success',
      message: 'Brand plan updated successfully',
      subscription: newSubscription
    });
  } catch (error) {
    console.error('Error updating brand plan:', error);
    res.status(500).json({
      status: 'failed',
      message: 'Failed to update brand plan'
    });
  }
};

/**
 * GET /api/admin/brands/:brandId/plan-history
 * Get plan change history for a brand
 */
const getBrandPlanHistory = async (req, res) => {
  try {
    const { brandId } = req.params;

    const history = await BrandPlanHistory
      .find({ brand: brandId })
      .sort({ changeDate: -1 })
      .limit(50);

    const formattedHistory = history.map(entry => ({
      date: entry.changeDate,
      oldPlan: entry.oldPlan,
      newPlan: entry.newPlan,
      planType: entry.newPlanType,
      validityPeriod: entry.newValidityStart && entry.newValidityEnd 
        ? `${entry.newValidityStart.toISOString().split('T')[0]} ~ ${entry.newValidityEnd.toISOString().split('T')[0]}`
        : null,
      extraQuota: entry.newExtraQuota,
      oldExtraQuota: entry.oldExtraQuota,
      campaignLimit: entry.newExtraCampaignLimit,
      oldCampaignLimit: entry.oldExtraCampaignLimit,
      creatorLimit: entry.newExtraCreatorLimit,
      oldCreatorLimit: entry.oldExtraCreatorLimit,
      adminUser: entry.changedBy
    }));

    res.json({
      status: 'success',
      history: formattedHistory
    });
  } catch (error) {
    console.error('Error fetching brand plan history:', error);
    res.status(500).json({
      status: 'failed',
      message: 'Failed to fetch plan history'
    });
  }
};

module.exports = {
  getBrandsWithPlans,
  updateBrandPlan,
  getBrandPlanHistory
};
