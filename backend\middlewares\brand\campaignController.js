const { Campaign, BrandCampaignRequest } = require('../../database');

// @desc    Get campaign statistics for the authenticated brand
// @route   GET /brand/campaign-request/stats
// @access  Private
const getCampaignStats = async (req, res) => {
  try {
    const brandId = req.user._id;
    const today = new Date();

    // Step 1: Fetch all campaigns for this brand
    const campaigns = await Campaign.find({ brandId });

    if (!campaigns.length) {
      return res.status(404).json({ message: 'No campaigns found for this brand.' });
    }

    // Step 2: Initialize statistics
    const stats = {
      total: campaigns.length,
      draft: 0,
      pending: 0,
      active: 0,
      completed: 0,
      averageSubmissionRate: 0, // TODO: Implement actual logic if needed
    };

    // Step 3: Categorize campaigns
    campaigns.forEach(({ status, recruitmentEndDate }) => {
      const normalizedStatus = status?.trim().toLowerCase();
      const isExpired = recruitmentEndDate && new Date(recruitmentEndDate) < today;

      if (normalizedStatus === 'draft') {
        stats.draft++;
      } else if (normalizedStatus === 'pending review') {
        stats.pending++;
      } else if (normalizedStatus === 'completed') {
        stats.completed++;
      } else {
        if (isExpired) {
          stats.completed++;
        } else {
          stats.active++;
        }
      }
    });

    // Step 4: Return stats
    return res.status(200).json(stats);
  } catch (error) {
    console.error('[Campaign Stats Error]:', error);
    return res.status(500).json({ message: 'Unable to fetch campaign statistics.' });
  }
};


module.exports = {
  getCampaignStats,
};
