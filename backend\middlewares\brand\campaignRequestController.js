const { BrandCampaignRequest, BrandSubscription, Campaign,campaignSubmission, appliedCampaigns, Brand } = require('../../database/index');
const sendEmail = require('../../utils/sendEmail');

/**
 * Create a new campaign request (Brand side)
 */
async function requestNewCampaign(req, res) {
  try {
    const brandId = req.user.id;
    const data = req.body;
    
    // 1) Verify active subscription and campaign limit (including add-ons)
    const subscription = await BrandSubscription.findOne({ brand: brandId, status: 'active' }).populate('plan');
    if (!subscription) {
      return res.status(403).json({ status: 'failed', message: 'No active subscription found' });
    }
    const totalAllowed = (subscription.plan.campaignsAllowed || 0) + (subscription.extraCampaignsAllowed || 0);
    if (subscription.campaignsUsed >= totalAllowed) {
      return res.status(403).json({ status: 'failed', message: 'Campaign limit reached' });
    }

    // 2) Create request
    const request = await BrandCampaignRequest.create({ ...data, brand: brandId });

    const brand= await Brand.findById(brandId)

    // 3) Increment used campaigns count
    subscription.campaignsUsed += 1;
    await subscription.save();
    sendEmail( {
      userName: request.brandName,
      campaignTitle: request.campaignTitle,
      templateName: 'campaign_submitted',
      subject: 'Your campaign is under review',
      to: brand.email
    })

    return res.status(201).json({ status: 'success', request });
  } catch (err) {
    console.error('❌ requestNewCampaign error:', err);
    return res.status(500).json({ status: 'failed', message: 'Server error creating campaign request' });
  }
}

/**
 * Get all campaign requests for the authenticated brand
 */
async function getMyCampaignRequests(req, res) {
  try {
    const brandId = req.user.id;

    // Step 1: Fetch all campaigns for the brand
    let campaigns = await Campaign.find({brandId})
      .sort({ createdAt: -1 })
      .populate("referenceId");
     campaigns = campaigns.filter(c =>
      c.referenceId?.brand?.toString() === brandId
    );

    const campaignIds = campaigns.map(c => c._id);

    // Step 2: Get approved applicants for these campaigns
    const approvedApps = await appliedCampaigns.find({
      campaign: { $in: campaignIds },
      status: "Approved"
    });

    // Build map: campaignId → Set of approved emails
    const approvedMap = {};
    approvedApps.forEach(app => {
      const campaignId = app.campaign.toString();
      const email = app.email.toLowerCase().trim();
      if (!approvedMap[campaignId]) approvedMap[campaignId] = new Set();
      approvedMap[campaignId].add(email);
    });

    // Step 3: Get submissions with URLs
    const submissions = await campaignSubmission.find({
      campaign_id: { $in: campaignIds },
      status: "submitted",
      $or: [
        { instagram_urls: { $exists: true, $not: { $size: 0 } } },
        { tiktok_urls: { $exists: true, $not: { $size: 0 } } }
      ]
    });

    // Build map: campaignId → Set of submitted emails
    const submissionMap = {};
    submissions.forEach(sub => {
      const campaignId = sub.campaign_id.toString();
      const email = sub.email.toLowerCase().trim();
      if (!submissionMap[campaignId]) submissionMap[campaignId] = new Set();
      submissionMap[campaignId].add(email);
    });

    // Step 4: Build final campaign stats
    const campaignsWithStats = campaigns.map(c => {
      const id = c._id.toString();

      const approvedEmails = approvedMap[id] || new Set();
      const submittedEmails = submissionMap[id] || new Set();

      let submittedCount = 0;
      submittedEmails.forEach(email => {
        if (approvedEmails.has(email)) submittedCount++;
      });

      const approvedCount = approvedEmails.size;
      const submissionRate = approvedCount > 0
        ? (submittedCount / approvedCount) * 100
        : 0;

      return {
        ...c.toObject(),
        approvedCount,
        submittedCount,
        submissionRate: Number(submissionRate.toFixed(2)),
      };
    });

    return res.json({
      status: "success",
      requests: campaignsWithStats
    });

  } catch (err) {
    console.error("❌ getMyCampaignRequests error:", err);
    return res.status(500).json({
      status: "failed",
      message: "Server error fetching campaign requests"
    });
  }
}






/**
 * Get a single campaign request by ID (Brand side)
 */
async function getCampaignRequestById(req, res) {
  try {
    const brandId = req.user.id;
    const { id } = req.params;
    const request = await BrandCampaignRequest.findOne({ _id: id, brand: brandId });
    if (!request) {
      return res.status(404).json({ status: 'failed', message: 'Request not found' });
    }
    return res.json({ status: 'success', request });
  } catch (err) {
    console.error('❌ getCampaignRequestById error:', err);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching request' });
  }
}

/**
 * Update a pending campaign request (Brand side)
 */
async function updateCampaignRequest(req, res) {
  try {
    const brandId = req.user.id;
    const { id } = req.params;
    const data = req.body;

    // Only allow update if request is pending
    const request = await BrandCampaignRequest.findOne({ _id: id, brand: brandId, approvalStatus: 'Pending' });
    if (!request) {
      return res.status(404).json({ status: 'failed', message: 'Request not found or cannot be updated' });
    }

    Object.assign(request, data);
    await request.save();
    return res.json({ status: 'success', request });
  } catch (err) {
    console.error('❌ updateCampaignRequest error:', err);
    return res.status(500).json({ status: 'failed', message: 'Server error updating campaign request' });
  }
}

/**
 * Delete (withdraw) a pending campaign request (Brand side)
 */
async function deleteCampaignRequest(req, res) {
  try {
    const brandId = req.user.id;
    const { id } = req.params;

    // Delete pending request
    const deleted = await BrandCampaignRequest.findOneAndDelete({ _id: id, brand: brandId, approvalStatus: 'Pending' });
    if (!deleted) {
      return res.status(404).json({ status: 'failed', message: 'Delete Request process only for Pending requests' });
    }

    // Decrement used campaigns count on subscription
    const subscription = await BrandSubscription.findOne({ brand: brandId, status: 'active' });
    if (subscription) {
      subscription.campaignsUsed = Math.max((subscription.campaignsUsed || 1) - 1, 0);
      await subscription.save();
    }

    return res.json({ status: 'success', message: 'Campaign request withdrawn successfully' });
  } catch (err) {
    console.error('❌ deleteCampaignRequest error:', err);
    return res.status(500).json({ status: 'failed', message: 'Server error deleting campaign request' });
  }
}

module.exports = {
  requestNewCampaign,
  getMyCampaignRequests,
  getCampaignRequestById,
  updateCampaignRequest,
  deleteCampaignRequest,
};
