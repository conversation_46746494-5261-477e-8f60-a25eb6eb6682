const { Types } = require('mongoose');
const {
  Campaign,
  campaignSubmission,
  appliedCampaigns,
  ExtensionRequest,
} = require('../../database/index');

const PRIORITY = { tracking: 4, content: 3, extension: 2, application: 1, recruitment: 0 };

const description = (type, extra = {}) => {
  switch (type) {
    case 'tracking':
      return `Enter tracking number for ${extra.creatorName || extra.email}`;
    case 'content':
      return `Review submitted content from ${extra.creatorName || extra.email}`;
    case 'extension':
      return `Review extension request (+${extra.daysRequested} days) from ${extra.creatorName}`;
    case 'application':
      return `Review application from ${extra.creatorName}`;
    default:
      return 'Campaign has 0 applications – needs promotion';
  }
};

const normalise = (doc) => ({
  id          : `${doc.type}-${doc._id.toString()}`, // Make ID unique by combining type and _id
  type        : doc.type,
  campaignId  : doc.campaignId?.toString(),
  campaignTitle: doc.campaignTitle,
  creatorId   : doc.creatorId || null,
  creatorName : doc.creatorName || null,
  description : description(doc.type, doc),
  priority    : PRIORITY[doc.type] ?? 0,
  createdAt   : doc.createdAt || new Date(),
  dueDate     : doc.dueDate || null,
  metadata    : doc.metadata || {},
});
 



exports.getTasks = async (req, res) => {
  try {
    const { type = 'all', search = '', brand = req.user._id, page = 1, limit = 10 } = req.query;

    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 10;

    const isValidId = Types.ObjectId.isValid(brand);
    const brandFilter = isValidId ? { 'campaign.brandId': new Types.ObjectId(brand) } : {};
    console.log('[getTasks] Brand filter:', brandFilter);

    // Aggregations for different task types
    const tracking = await campaignSubmission.aggregate([
      {
        $match: {
          $or: [
            { 'tracking_info.tracking_number': '' },
            { 'tracking_info.tracking_number': { $exists: false } },
          ],
        },
      },
      {
        $lookup: {
          from: 'campaigns',
          localField: 'campaign_id',
          foreignField: '_id',
          as: 'campaign',
        },
      },
      { $unwind: '$campaign' },
      { $match: brandFilter },
      {
        $project: {
          _id: '$_id',
          type: { $literal: 'tracking' },
          campaignId: '$campaign._id',
          campaignTitle: '$campaign.campaignTitle',
          creatorId: '$user_id',
          creatorName: '$email',
          dueDate: '$campaign.deadline',
        },
      },
    ]);

    const content = await campaignSubmission.aggregate([
      { $match: { content_status: 'Pending' } },
      {
        $lookup: {
          from: 'campaigns',
          localField: 'campaign_id',
          foreignField: '_id',
          as: 'campaign',
        },
      },
      { $unwind: '$campaign' },
      { $match: brandFilter },
      {
        $project: {
          _id: '$_id',
          type: { $literal: 'content' },
          campaignId: '$campaign._id',
          campaignTitle: '$campaign.campaignTitle',
          creatorId: '$user_id',
          creatorName: '$email',
          dueDate: '$campaign.deadline',
        },
      },
    ]);

    const extension = await ExtensionRequest.aggregate([
      { $match: { status: 'pending' } },
      {
        $lookup: {
          from: 'campaigns',
          localField: 'campaign',
          foreignField: '_id',
          as: 'campaign',
        },
      },
      { $unwind: '$campaign' },
      { $match: brandFilter },
      {
        $project: {
          _id: '$_id',
          type: { $literal: 'extension' },
          campaignId: '$campaign._id',
          campaignTitle: '$campaign.campaignTitle',
          creatorId: '$creator',
          creatorName: '$creatorName',
          daysRequested: '$daysRequested',
          dueDate: '$campaign.deadline',
        },
      },
    ]);

    const application = await appliedCampaigns.aggregate([
      { $match: { status: 'Pending' } },
      {
        $lookup: {
          from: 'campaigns',
          localField: 'campaign',
          foreignField: '_id',
          as: 'campaign',
        },
      },
      { $unwind: '$campaign' },
      { $match: brandFilter },
      {
        $project: {
          _id: '$_id',
          type: { $literal: 'application' },
          campaignId: '$campaign._id',
          campaignTitle: '$campaign.campaignTitle',
          creatorId: '$creator',
          creatorName: '$name',
          dueDate: '$campaign.recruitmentEndDate',
        },
      },
    ]);

    const recruitment = await Campaign.aggregate([
      { $match: { ...brandFilter, status: { $in: ['Active', 'Pending'] } } },
      {
        $lookup: {
          from: 'appliedcampaigns',
          localField: '_id',
          foreignField: 'campaign',
          as: 'apps',
        },
      },
      { $addFields: { applicationCount: { $size: '$apps' } } },
      { $match: { applicationCount: 0 } },
      {
        $project: {
          _id: '$_id',
          type: { $literal: 'recruitment' },
          campaignId: '$_id',
          campaignTitle: '$campaignTitle',
          dueDate: '$recruitmentEndDate',
        },
      },
    ]);

    let tasks = [
      ...tracking,
      ...content,
      ...extension,
      ...application,
      ...recruitment,
    ].map(normalise);

    // Filtering by search
  if (search) {
  const s = search.toLowerCase();
  tasks = tasks.filter((t) =>
    t.campaignTitle.toLowerCase().includes(s) ||
    t.creatorName?.toLowerCase().includes(s) ||
    t.description.toLowerCase().includes(s)
  );
}


    // Filtering by type
    if (type !== 'all') {
      tasks = tasks.filter((t) => t.type === type);
    }

    // Sorting by priority and due date
    tasks.sort((a, b) =>
      PRIORITY[b.type] - PRIORITY[a.type] ||
      new Date(a.dueDate || Infinity) - new Date(b.dueDate || Infinity)
    );

    // Pagination calculations
    const startIndex = (pageNum - 1) * limitNum;
    const paginatedTasks = tasks.slice(startIndex, startIndex + limitNum);
    const totalPages = Math.ceil(tasks.length / limitNum);

    // Summary object
    const summary = {
      total: tasks.length,
      byType: Object.keys(PRIORITY).reduce((acc, k) => {
        acc[k] = tasks.filter((t) => t.type === k).length;
        return acc;
      }, {}),
    };

    // Send response
    res.json({
      status: 'success',
      tasks: paginatedTasks,
      summary,
      pagination: {
        page: pageNum,
        limit: limitNum,
        totalPages,
      },
    });
  } catch (err) {
    console.error('[getTasks]', err);
    res.status(500).json({ status: 'failed', message: 'Server error' });
  }
};


exports.completeTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { type, payload = {} } = req.body;
    if (!type)
      return res.status(400).json({ status: 'failed', message: 'type is required' });

    // Extract the original _id from the task ID (format: "type-_id")
    const originalId = taskId.includes('-') ? taskId.split('-')[1] : taskId;
    const _id = Types.ObjectId(originalId);

    switch (type) {
      case 'tracking': {
        const { tracking_number, courier = '', tracking_link = '' } = payload;
        if (!tracking_number)
          return res
            .status(400)
            .json({ status: 'failed', message: 'tracking_number required' });

        await campaignSubmission.updateOne(
          { _id },
          {
            $set: {
              'tracking_info.tracking_number': tracking_number,
              'tracking_info.courier': courier,
              'tracking_info.tracking_link': tracking_link,
              'tracking_info.last_updated': new Date(),
            },
          }
        );
        break;
      }

      case 'content': {
        const { reviewStatus = 'Approved' } = payload;
        await campaignSubmission.updateOne(
          { _id },
          {
            $set: {
              content_status: reviewStatus,
              status: reviewStatus === 'Approved' ? 'reviewed' : 'rejected',
            },
          }
        );
        break;
      }

      case 'extension': {
        const { decision = 'approved' } = payload;
        await ExtensionRequest.updateOne(
          { _id },
          { $set: { status: decision, decisionAt: new Date() } }
        );
        break;
      }

      case 'application': {
        const { decision = 'Approved', rejectionReason = '' } = payload;
        await appliedCampaigns.updateOne(
          { _id },
          { $set: { status: decision, rejectionReason } }
        );
        break;
      }

      case 'recruitment': {
        await Campaign.updateOne({ _id }, { $set: { recruiting: 1 } });
        break;
      }

      default:
        return res.status(400).json({ status: 'failed', message: 'Unknown task type' });
    }

    res.json({ status: 'success', message: 'Task updated' });
  } catch (err) {
    console.error('[completeTask]', err);
    res.status(500).json({ status: 'failed', message: 'Server error' });
  }
};

