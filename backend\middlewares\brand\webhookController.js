const Stripe = require('stripe');
const { addMonths, addDays } = require('date-fns');
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

const {
  BrandSubscription,
  PaymentHistory,
  BrandPlan
} = require('../../database/index');

const {
  sendSubscriptionActivatedEmail,
  sendUpgradeConfirmationEmail
} = require('../../functions/sendEmail');

/**
 * Handle Stripe webhooks at /webhooks/stripe
 */
async function handleStripeWebhook(req, res) {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    console.log("✅ Stripe webhook received:", event.type);
  } catch (err) {
    console.error('❌ Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  if (event.type === 'checkout.session.completed') {
    const session = event.data.object;
    console.log("🔔 Checkout session completed:", session.id);

    const {
      brandId,
      planId,
      isUpgrade,
      upgrade_from,
      isAddon,
      addonType,
      quantity,
      subscriptionId
    } = session.metadata || {};

    console.log("📍 Metadata:", session.metadata);


    const now = new Date();
    const expiresAt = addMonths(now, 6); // safely adds 6 months
    const upgradeWindowEnds = addDays(now, 7); // safely adds 7 days
    
    try {
      // ── 1) ADD-ON FLOW ─────────────────────────────────────
      if (isAddon === 'true') {
        console.log("✳️ Processing add-on:", addonType, quantity);

        // bump the extra allowance
        const incField = addonType === 'campaign'
          ? { extraCampaignsAllowed: Number(quantity) }
          : { extraCreatorsAllowed:  Number(quantity) };

        await BrandSubscription.findByIdAndUpdate(
          subscriptionId,
          { $inc: incField },
          { new: true }
        );

        // record in payment history
        await PaymentHistory.create({
          brand:           brandId,
          plan:            planId,
          type:            'addon',
          amountPaid:      Number(session.amount_total) / 100,
          stripeSessionId: session.id,
          metadata:        { addonType, quantity }
        });

        console.log("✅ Add-on applied and history recorded");
      
      // ── 2) UPGRADE FLOW ───────────────────────────────────
      } else if (isUpgrade === 'true') {
        console.log("⬆️ Processing upgrade...");

        // mark old sub upgraded
        await BrandSubscription.updateOne(
          { brand: brandId, status: 'active' },
          { status: 'upgraded' }
        );

        // create new upgraded sub
        const upgraded = await BrandSubscription.create({
          brand:               brandId,
          plan:                planId,
          purchaseDate:        now,
          expiresAt,
          upgradedFrom:        upgrade_from,
          stripeSessionId:     session.id,
          status:              'active',
          upgradeEligibleTill: upgradeWindowEnds
        });
        console.log("✅ New upgraded subscription:", upgraded);

        // send email
        const newPlan = await BrandPlan.findById(planId);
        await sendUpgradeConfirmationEmail(brandId, newPlan.name);

        // record in payment history
        await PaymentHistory.create({
          brand:           brandId,
          plan:            planId,
          type:            'upgrade',
          amountPaid:      Number(session.amount_total) / 100,
          stripeSessionId: session.id,
          metadata:        session.metadata
        });

      // ── 3) NEW PURCHASE FLOW ──────────────────────────────
      } else {
        console.log("🆕 Processing new plan purchase...");

        const created = await BrandSubscription.create({
          brand:               brandId,
          plan:                planId,
          purchaseDate:        now,
          expiresAt,
          stripeSessionId:     session.id,
          status:              'active',
          upgradeEligibleTill: upgradeWindowEnds
        });
        console.log("✅ New subscription created:", created);

        const newPlan = await BrandPlan.findById(planId);
        await sendSubscriptionActivatedEmail(brandId, newPlan.name);

        // record in payment history
        await PaymentHistory.create({
          brand:           brandId,
          plan:            planId,
          type:            'purchase',
          amountPaid:      Number(session.amount_total) / 100,
          stripeSessionId: session.id,
          metadata:        session.metadata
        });
      }
 
    } catch (processErr) {
      console.error("❌ Error processing webhook:", processErr);
      return res.status(500).json({ status: "failed", message: processErr.message });
    }
  }

  res.json({ received: true });
}

module.exports = { handleStripeWebhook };
