const { PaymentHistory } = require('../database/index');

async function getAllPaymentHistory(req, res) {
  try {
    const history = await PaymentHistory
      .find({})
      .populate('brand')
      .populate('plan')
      .sort({ createdAt: -1 });

    res.json({ status: "success", history });
  } catch (err) {
    console.error("❌ Error fetching admin payment history:", err);
    res.status(500).json({ status: "failed", message: "Something went wrong" });
  }
}

module.exports = { getAllPaymentHistory };
