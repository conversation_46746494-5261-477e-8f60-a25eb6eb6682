const express = require("express");
const { Campaign, appliedCampaigns, campaignSubmission, CreatorAccessRequest, BlockedCreator, User } = require("../database");
const { verifyToken } = require("../utils/jwtUtils");
const CampaignsPublic = express.Router();

/**
 * Helper: Checks if the campaign is still recruiting or closed
 * based on deadline, seats filled, and active status.
 */
function computeCampaignStatus(campaign, approvedCount) {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Normalize to start of day

  let deadlineDate = null;
  if (campaign.deadline) {
    deadlineDate = new Date(campaign.deadline);
    if (!isNaN(deadlineDate)) {
      deadlineDate.setHours(0, 0, 0, 0);
    } else {
      deadlineDate = null;
    }
  }

  const isExpired = deadlineDate ? deadlineDate < today : false;

  const isFull =
    typeof campaign.recruiting === "number" &&
    campaign.recruiting !== -1 &&
    approvedCount >= campaign.recruiting;

  const isActive = campaign.status === "Active";

  return isActive && !isExpired && !isFull ? "Recruiting" : "Closed";
}

// ————————— GET /active —————————
// Returns only campaigns that are still open by date
CampaignsPublic.get("/active", async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Local midnight

    const campaigns = await Campaign.find({
      deadline: { $gte: today },
    });

    const result = await Promise.all(
      campaigns.map(async (c) => {
        const approvedCount = await appliedCampaigns.countDocuments({
          campaign: c._id,
          status: "Approved",
        });

        return {
          id: c._id,
          name: c.campaignTitle,
          brand: c.brandName,
          deadline: c.deadline,
          recruitmentEndDate: c.recruitmentEndDate,
          category: c.contentFormat,
          image: c.brandLogo,
          recruiting: c.recruiting,
          applicantsCount: approvedCount,
          campaignStatus: computeCampaignStatus(c, approvedCount),
          status: c.status,
        };
      })
    );

    res.json({ status: "success", campaigns: result });
  } catch (err) {
    console.error("❌ [GET /active] Error:", err);
    res.status(500).json({ status: "failed", message: err.message });
  }
});

// ————————— GET /?page=… —————————
// Paginated campaigns with sorting and campaignStatus logic
CampaignsPublic.get("/", async (req, res) => {
  const page = parseInt(req.query.page, 10) || 1;
  const limit = 6;
  const skip = (page - 1) * limit;
  const type = req?.query?.type || "gifted";

  try {
    const matchStage = { status: "Active", campaignType: type };

    const campaigns = await Campaign.aggregate([
      { $match: matchStage },
      {
        $addFields: {
          hashPriority: {
            $cond: [{ $eq: [{ $substrCP: ["$brandName", 0, 1] }, "#"] }, 1, 0],
          },
        },
      },
      { $sort: { hashPriority: -1, deadline: -1, _id: 1 } },
      { $skip: skip },
      { $limit: limit },
    ]);

    if (!campaigns.length) {
      return res.json({ status: "failed", message: "No campaigns found" });
    }

    const campaignIds = campaigns.map((c) => c._id);

    const counts = await appliedCampaigns.aggregate([
      { $match: { campaign: { $in: campaignIds }, status: "Approved" } },
      { $group: { _id: "$campaign", count: { $sum: 1 } } },
    ]);

    const countMap = {};
    counts.forEach((item) => {
      countMap[item._id.toString()] = item.count;
    });

    // Get user ID from token if available
    const token = req.headers.authorization;

    let user = null;
    if (token) {
        const decoded = await verifyToken(token);
            console.log("Token:", decoded);
    
        user = await User.findById(decoded.id).select("_id");
        
    }
const enriched = await Promise.all(
  campaigns.map(async (c) => {
    const approvedCount = countMap[c._id.toString()] || 0;
    const isUnlimited = c.recruiting === -1;

    const campaignStatus = computeCampaignStatus(c, approvedCount);

    // Check if blocked
    const blockedEntry = await BlockedCreator.findOne({
      brandId: c.brandId,
      creatorId: user ? user?._id : null,
    });
    console.log("Blocked entry:", blockedEntry);

    // Add a flag for blocked or not (optional)
    const isBlocked = c._id == "682f58586fda45dba090da7c" ? false : !!blockedEntry;

    return {
      id: c._id,
      name: c.campaignTitle,
      product: c.productName
        ? String(c.productName)
            .split(" ")
            .slice(0, 4)
            .join(" ")
        : null,
      brand: c.brandName,
      deadline: c.deadline,
      recruitmentEndDate: c.recruitmentEndDate,
      category: c.contentFormat,
      image: c.brandLogo,
      recruiting: c.recruiting,
      applicantsCount: approvedCount,
      isUnlimited,
      campaignStatus,
      status: c.status,
      campaignType: c.campaignType,
      type: c.campaignType,
      isBlocked, // Include block status for frontend logic
    };
  })
);


    // ✅ Filter out logically closed campaigns
    const openCampaigns = enriched.filter((c) => c.campaignStatus !== "Closed");
  console.log("Open campaigns:", enriched);
    if (!openCampaigns.length) {
      return res.json({ status: "failed", message: "No open campaigns available" });
    }

    const total = await Campaign.countDocuments(matchStage);
    const totalPages = Math.ceil(total / limit);

    res.json({
      status: "success",
      campaigns: openCampaigns,
      totalPages,
    });
  } catch (err) {
    console.error("❌ [GET /] Error:", err);
    res.status(500).json({ status: "failed", message: err.message });
  }
});

// ————————— GET /:id/:email —————————
// Single campaign details and whether the user has applied
CampaignsPublic.get("/:id/:email", async (req, res) => {
  const { id, email } = req.params;

  try {
    const campaign = await Campaign.findById(id);
    if (!campaign) {
      return res
        .status(404)
        .json({ status: "failed", message: "Campaign not found" });
    }

    const approvedCount = await appliedCampaigns.countDocuments({
      campaign: id,
      status: "Approved",
    });

    const campaignStatus = computeCampaignStatus(campaign, approvedCount);

    const exists = await appliedCampaigns.findOne({ email, campaign: id });

    const detail = {
      ...campaign.toObject(),
      applied: !!exists,
    };

    res.json({
      status: "success",
      campaign: detail,
      applicantsCount: approvedCount,
      campaignStatus,
    });
  } catch (err) {
    console.error("❌ [GET /:id/:email] Error:", err);
    res.status(500).json({ status: "failed", message: err.message });
  }
});

module.exports = { CampaignsPublic };
