// ✅ File: middlewares/rewardRedeem.js

const { User, rewardTier, rewardTransaction, pointEventLog , referralReward} = require("../database");
const { sendRedemptionEmailToAdmin } = require("../functions/sendEmail");


// Redeem reward
async function redeemReward(req, res) {
  try {
    const userId = req.user._id;
    const { tierId } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ status: "failed", message: "User not found" });
    }

    const tier = await rewardTier.findById(tierId);
    if (!tier || !tier.active) {
      return res.status(400).json({ status: "failed", message: "Invalid reward tier" });
    }

    // ✅ Calculate total approved count including referral bonus
    const approvedCount = await pointEventLog.countDocuments({
      user: userId,
      source: "content-approval",
    });

    const referralBonusCount = await referralReward.countDocuments({
      referrer: userId,
      rewardGiven: true,
      revoked: false,
    });

    const totalApprovedCount = approvedCount + referralBonusCount;

    if (totalApprovedCount < tier.pointsRequired) {
      return res.status(400).json({
        status: "failed",
        message: `You need ${tier.pointsRequired} approved posts to redeem this reward`,
      });
    }

    // Create transaction
    const transaction = await rewardTransaction.create({
      user: userId,
      rewardTier: tier._id,
      status: "pending",
    });

    // Log redemption event
    await PointEventLog.create({
      user: userId,
      source: "redeem",
      points: 0, // Optional: keep points for audit
      note: `${tier.rewardLabel} redeemed`,
    });

    // Send email to admin
    await sendRedemptionEmailToAdmin({
      userEmail: user.email,
      userName: user.name,
      rewardLabel: tier.rewardLabel,
      pointsUsed: tier.pointsRequired,
    });

    return res.json({
      status: "success",
      message: `You have successfully redeemed ${tier.rewardLabel}`,
      transactionId: transaction._id,
    });
  } catch (err) {
    console.error("❌ Redemption error:", err);
    return res.status(500).json({ status: "failed", message: "Server error" });
  }
}

module.exports = {
  redeemReward,
};
