{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node start.js", "dev": "nodemon server.js", "debug": "node railway-debug.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cheerio": "^1.1.2", "cloudinary": "^1.41.3", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "express-fileupload": "^1.5.1", "express-rate-limit": "^7.5.0", "google-auth-library": "^9.15.1", "gridfs-stream": "^1.1.1", "helmet": "^8.1.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.14.1", "multer": "^2.0.0", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^4.2.1", "nodemailer": "^6.10.0", "openai": "^5.8.3", "p-queue": "^8.1.0", "path": "^0.12.7", "puppeteer": "^22.15.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "stripe": "^18.2.0", "validator": "^13.15.0"}, "devDependencies": {"nodemon": "^3.1.10"}}