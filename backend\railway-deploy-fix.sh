#!/bin/bash

# Railway Deployment Fix Script for Guideway Consulting Backend

echo "🚂 Railway Deployment Fix for Guideway Consulting"
echo "=================================================="
echo ""

echo "🔧 Step 1: Checking current deployment status..."
echo ""

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Please install it first:"
    echo "   npm install -g @railway/cli"
    echo "   railway login"
    exit 1
fi

echo "✅ Railway CLI found"

echo ""
echo "🔧 Step 2: Verifying environment variables..."
echo ""

# List of required environment variables for Railway
REQUIRED_VARS=(
    "NODE_ENV"
    "MONGO_URL"
    "SECRET_KEY"
    "CLOUDINARY_CLOUD_NAME"
    "CLOUDINARY_API_KEY"
    "CLOUDINARY_API_SECRET"
    "FRONTEND_URL"
    "ADMIN_USER"
    "ADMIN_HASHED_PASS"
    "STRIPE_SECRET_KEY"
    "BREVO_SMTP_HOST"
    "BREVO_SMTP_USER"
    "BREVO_SMTP_PASS"
    "BREVO_API_KEY"
    "RECAPTCHA_SECRET_KEY"
)

echo "📋 Required environment variables for Railway:"
for var in "${REQUIRED_VARS[@]}"; do
    echo "   - $var"
done

echo ""
echo "🔧 Step 3: Setting Railway environment variables..."
echo ""

# Set environment variables (you'll need to run these manually in Railway dashboard)
echo "🔧 Please set these in Railway Dashboard → Your Project → Variables:"
echo ""
echo "NODE_ENV=production"
echo "MONGO_URL=mongodb+srv://abhisheksingh4115:<EMAIL>/Practice-db?retryWrites=true&w=majority"
echo "SECRET_KEY=iREJP1r+kdPWJ7VWcz/BtXibAWhFTEIKnWdGiZ9ZAI4="
echo "ADMIN_USER=<EMAIL>"
echo "ADMIN_HASHED_PASS=\$2b\$10\$kthSu.4WHdClXAhEdcEbTeChxrfKC0hqidI3sDJv3uDTTa56KjbR2"
echo "CLOUDINARY_CLOUD_NAME=drujwoine"
echo "CLOUDINARY_API_KEY=683974564338127"
echo "CLOUDINARY_API_SECRET=J8UG31wXEm-reuECSmdmQbSy3c4"
echo "FRONTEND_URL=https://guideway-consulting.vercel.app"
echo "RECAPTCHA_SECRET_KEY=6Ld-zC4rAAAAAAVVbpXmfeq9st9SZh0nT-BAceSS"
echo "BREVO_SMTP_HOST=smtp-relay.brevo.com"
echo "BREVO_SMTP_PORT=587"
echo "BREVO_SMTP_USER=<EMAIL>"
echo "BREVO_SMTP_PASS=6rnTYQPSdMafC8Wq"
echo "BREVO_API_KEY=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"
echo "STRIPE_SECRET_KEY=sk_test_51RU2HJPFPsuRFy0ndsdzUcivQi0Q2PR4DCjtyRBsloQSfCAI1COvf9O4PV4tCnjk93sOpsuQJa5x3NdybecqrWAa00IYq3WbZN"
echo "STRIPE_WEBHOOK_SECRET=whsec_9rCtrGn1a9YQrals5gpJWjgdErLwZSdv"

echo ""
echo "🔧 Step 4: Deployment checklist..."
echo ""

echo "✅ Health check endpoint: /health (configured before middleware)"
echo "✅ MongoDB connection: Fixed database name access"
echo "✅ PORT configuration: Uses Railway's dynamic PORT"
echo "✅ CORS: Configured for Vercel frontend"
echo "✅ Rate limiting: Excludes health check endpoint"
echo "✅ Dockerfile: Optimized for Railway"
echo "✅ Dependencies: Updated multer to fix security vulnerability"

echo ""
echo "🚀 Step 5: Manual deployment steps..."
echo ""

echo "1. Go to Railway Dashboard: https://railway.app/dashboard"
echo "2. Select your project: guideway-consulting-production"
echo "3. Go to Variables tab and set all environment variables above"
echo "4. Go to Settings → Deploy and trigger a new deployment"
echo "5. Monitor the build logs for any errors"
echo "6. Test the health check: https://guideway-consulting-production.up.railway.app/health"

echo ""
echo "🧪 Step 6: Testing endpoints..."
echo ""

echo "After deployment, test these URLs:"
echo "• Health check: https://guideway-consulting-production.up.railway.app/health"
echo "• API root: https://guideway-consulting-production.up.railway.app/"
echo "• Admin auth: https://guideway-consulting-production.up.railway.app/api/admin"

echo ""
echo "🔍 Step 7: Troubleshooting..."
echo ""

echo "If health check still fails:"
echo "1. Check Railway logs for MongoDB connection errors"
echo "2. Verify all environment variables are set correctly"
echo "3. Ensure PORT is not set in environment variables (Railway sets it automatically)"
echo "4. Check if the application is binding to 0.0.0.0 (not localhost)"
echo "5. Verify the health check endpoint responds before middleware"

echo ""
echo "✅ Deployment fix script completed!"
echo "   Please follow the manual steps above to complete the deployment."
