#!/bin/bash

# Railway Deployment Script for Guideway Consulting Backend

echo "🚂 Railway Deployment Setup for Guideway Consulting"
echo "=================================================="
echo ""

echo "📋 Pre-deployment Checklist:"
echo "✅ Frontend URL: https://guideway-consulting.vercel.app"
echo "✅ Health check endpoint: /health"
echo "✅ Enhanced startup logging enabled"
echo "✅ MongoDB connection validation"
echo "✅ CORS configured for Vercel"
echo ""

echo "🔧 Required Railway Environment Variables:"
echo "NODE_ENV=production"
echo "FRONTEND_URL=https://guideway-consulting.vercel.app"
echo "MONGO_URL=mongodb+srv://abhisheksingh4115:<EMAIL>/Practice-db?retryWrites=true&w=majority"
echo "SECRET_KEY=iREJP1r+kdPWJ7VWcz/BtXibAWhFTEIKnWdGiZ9ZAI4="
echo "ADMIN_USER=<EMAIL>"
echo "ADMIN_HASHED_PASS=\$2b\$10\$kthSu.4WHdClXAhEdcEbTeChxrfKC0hqidI3sDJv3uDTTa56KjbR2"
echo "CLOUDINARY_CLOUD_NAME=drujwoine"
echo "CLOUDINARY_API_KEY=683974564338127"
echo "CLOUDINARY_API_SECRET=J8UG31wXEm-reuECSmdmQbSy3c4"
echo "RECAPTCHA_SECRET_KEY=6Ld-zC4rAAAAAAVVbpXmfeq9st9SZh0nT-BAceSS"
echo "GOOGLE_CLIENT_ID=************-43j6p7k79nfi39d5u7knmai9nkla1tgb.apps.googleusercontent.com"
echo "BREVO_SMTP_HOST=smtp-relay.brevo.com"
echo "BREVO_SMTP_PORT=587"
echo "BREVO_SMTP_USER=<EMAIL>"
echo "BREVO_SMTP_PASS=6rnTYQPSdMafC8Wq"
echo "BREVO_SEND_EMAIL=<EMAIL>"
echo "BREVO_API_KEY=xkeysib-9ac78f59c620393b66c53ef7c0d0b12f59c4b20ddb58c9a40f112ba2d96ac7-vw0bNZ2V2DccA7iK"
echo "STRIPE_SECRET_KEY=sk_test_51RU2HJPFPsuRFy0ndsdzUcivQi0Q2PR4DCjtyRBsloQSfCAI1COvf9O4PV4tCnjk93sOpsuQJa5x3NdybecqrWAa00IYq3WbZN"
echo "STRIPE_WEBHOOK_SECRET=whsec_9rCtrGn1a9YQrals5gpJWjgdErLwZSdv"
echo ""

echo "🚀 Deployment Steps:"
echo "1. Set all environment variables in Railway Dashboard"
echo "2. Push code to GitHub repository"
echo "3. Railway will automatically deploy"
echo "4. Monitor deployment logs"
echo "5. Test health endpoint: https://your-railway-url.up.railway.app/health"
echo ""

echo "🔍 Post-deployment Testing:"
echo "curl https://your-railway-url.up.railway.app/health"
echo "curl https://your-railway-url.up.railway.app/"
echo ""

echo "📊 Expected Health Response:"
echo '{'
echo '  "status": "healthy",'
echo '  "timestamp": "2025-01-26T...",'
echo '  "port": "PORT_NUMBER",'
echo '  "database": "connected",'
echo '  "environment": "production"'
echo '}'
echo ""

echo "✅ Setup complete! Ready for Railway deployment."
