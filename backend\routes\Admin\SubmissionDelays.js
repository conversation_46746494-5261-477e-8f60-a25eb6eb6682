const express = require("express");
const router = express.Router();
const dayjs = require("dayjs");
const { User, Campaign, campaignSubmission } = require("../../database");

// GET /api/admin/submission-delays
router.get("/", async (req, res) => {
  try {
    // Fetch all campaigns
    const campaigns = await Campaign.find().lean();

    // Build results with delayed influencers
    const results = await Promise.all(
      campaigns.map(async (campaign) => {
        // Find submissions that are still 'submitted' and past deadline
        const delayedSubmissions = await campaignSubmission
          .find({
            campaign_id: campaign._id,
            status: "submitted",
            submitted_at: { $lt: campaign.deadline },
          })
          .populate("user_id", "name email phone")
          .lean();

        // Map to influencer info with safe defaults
        const delayedInfluencers = delayedSubmissions.map((s) => {
          const reminder = s.reminder_status || {};
          return {
            id: s._id,
            name: s.user_id?.name || "",
            email: s.user_id?.email || "",
            phone: s.user_id?.phone || "",
            email_count: reminder.email_count || 0,
            sms_count: reminder.sms_count || 0,
            dm_count: reminder.dm_count || 0,
            last_contact_date: reminder.last_contact_date || s.submitted_at,
            daysOverdue: dayjs().diff(dayjs(campaign.deadline), "day"),
          };
        });

        return {
          id: campaign._id,
          title: campaign.campaignTitle,
          type: campaign.campaignType,
          deadline: campaign.deadline,
          totalParticipants: await campaignSubmission.countDocuments({ campaign_id: campaign._id }),
          delayed: delayedInfluencers.length,
          delayedInfluencers,
        };
      })
    );

    res.json(results);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server error" });
  }
});

module.exports = router;
