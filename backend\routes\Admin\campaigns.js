const express = require("express");
const { VerifyTokenAuth } = require("../../middlewares/AdminAuth");

const { addCampaign, getCampaigns, editCmpaigns, deleteCampaigns, getBrands } = require("../../middlewares/campaign");
const {appliedCampaigns } = require("../../database");

const Campaigns = express.Router();

Campaigns.post("/addCampaign", VerifyTokenAuth, addCampaign);
Campaigns.get("/", getCampaigns);
Campaigns.post("/editCampaign/:id", VerifyTokenAuth, editCmpaigns);
Campaigns.delete("/:id", VerifyTokenAuth, deleteCampaigns);
Campaigns.get("/brands", VerifyTokenAuth, getBrands);

Campaigns.get('/campaign-applicants/:campaignId', VerifyTokenAuth, async (req, res) => {
  const { campaignId } = req.params;
  try {
    if (!campaignId) {
      return res.status(400).json({ status: "error", message: "Campaign ID is required" });
    }

    const submissions = await appliedCampaigns
      .find({ campaign: campaignId })
      .select('email')
      .sort({createdAt : 1 });

    const submittedEmails = submissions.map(sub => sub.email);

    return res.json({
      status: "success",
      data: submittedEmails.map(email => ({ email })),
    });
  } catch (err) {
    console.error("Error fetching applicants:", err);
    return res.status(500).json({
      status: "error",
      message: "Failed to fetch applicants",
    });
  }
});


module.exports = {
    Campaigns
}