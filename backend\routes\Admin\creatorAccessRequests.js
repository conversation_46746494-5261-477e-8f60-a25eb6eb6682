const express = require("express");
const { VerifyTokenAuth } = require("../../middlewares/AdminAuth");
const { User, CreatorAccessRequest } = require("../../database");

const adminCreatorAccessRouter = express.Router();

// GET /api/admin/car
// Admin-only route to get all pending requests
adminCreatorAccessRouter.get("/", VerifyTokenAuth, async (req, res) => {
  try {
    console.log("here 01");
    const requests = await CreatorAccessRequest.find({
      status: "pending",
    })
      .populate("creator", "name email")
      .sort({ createdAt: -1 });
      console.log(requests);

    const formattedRequests = requests.map((request) => ({
      id: request._id,
      creatorName: request.creator.name,
      creatorEmail: request.creator.email,
      platform: request.platform,
      socialHandle: request.socialHandle,
      socialLink: request.socialLink,
      giftedCampaignsSubmitted: request.giftedCampaignsSubmitted,
      requestDate: request.createdAt,
      status: request.status,
    }));

    res.json({
      status: "success",
      data: formattedRequests,
    });
  } catch (error) {
    console.error("Error fetching creator access requests:", error);
    res.status(500).json({
      status: "error",
      message: "Internal server error",
    });
  }
});

// PUT /api/admin/car/:id/approve
// Admin approves a request with follower count
adminCreatorAccessRouter.put("/:id/approve", VerifyTokenAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { followerCount } = req.body;
    console.log("here 02",followerCount,id);
    if (!followerCount || typeof followerCount !== "number" || followerCount <= 0) {
      return res.status(400).json({
        status: "error",
        message: "Valid follower count is required",
      });
    }

    console.log("03");
    const accessRequest = await CreatorAccessRequest.findById(id).populate("creator");
    if (!accessRequest) {
      return res.status(404).json({
        status: "error",
        message: "Access request not found",
      });
    }
    console.log("04");
    if (accessRequest.status !== "pending") {
      return res.status(400).json({
        status: "error",
        message: "Request is not pending",
      });
    }
    // Update the access request
    accessRequest.status = "approved";
    accessRequest.followerCount = followerCount;
    accessRequest.approvedBy = "60f7a1d7e3b2c437f0a9d1a2";
    accessRequest.approvedAt = new Date();
    await accessRequest.save();

    // Update the creator's paid_status
    console.log(accessRequest);
    if (accessRequest.creator && accessRequest.creator._id) {
      const creator = await User.findById(accessRequest.creator._id);
      if (creator) {
        creator.paid_status = "approved";
        await creator.save();
      }
    }

    res.json({
      status: "success",
      message: "Access request approved successfully",
      data: {
        id: accessRequest._id,
        status: accessRequest.status,
        followerCount: accessRequest.followerCount,
        approvedAt: accessRequest.approvedAt,
      },
    });
  } catch (error) {
    console.error("Error approving access request:", error);
    res.status(500).json({
      status: "error",
      message: "Internal server error",
    });
  }
});

// PUT /api/admin/car/:id/reject
// Admin rejects a request with reason
adminCreatorAccessRouter.put("/:id/reject", VerifyTokenAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    console.log("here 03");
    if (!reason || typeof reason !== "string" || reason.trim().length === 0) {
      return res.status(400).json({
        status: "error",
        message: "Rejection reason is required",
      });
    }

    const accessRequest = await CreatorAccessRequest.findById(id).populate("creator");
    if (!accessRequest) {
      return res.status(404).json({
        status: "error",
        message: "Access request not found",
      });
    }

    if (accessRequest.status !== "pending") {
      return res.status(400).json({
        status: "error",
        message: "Request is not pending",
      });
    }

    // Update the access request
    accessRequest.status = "rejected";
    accessRequest.rejectionReason = reason.trim();
    accessRequest.rejectedBy = "60f7a1d7e3b2c437f0a9d1a2";
    accessRequest.rejectedAt = new Date();;
    await accessRequest.save();

    // Update the creator's paid_status
    const creator = await User.findById(accessRequest.creator._id);
    if (creator) {
      creator.paid_status = "rejected";
      await creator.save();
    }

    res.json({
      status: "success",
      message: "Access request rejected successfully",
      data: {
        id: accessRequest._id,
        status: accessRequest.status,
        rejectionReason: accessRequest.rejectionReason,
        rejectedAt: accessRequest.rejectedAt,
      },
    });
  } catch (error) {
    console.error("Error rejecting access request:", error);
    res.status(500).json({
      status: "error",
      message: "Internal server error",
    });
  }
});

// GET /api/admin/car/stats
// Get statistics for admin dashboard
adminCreatorAccessRouter.get("/stats", VerifyTokenAuth, async (req, res) => {
  try {
    console.log("here 04");
    const pendingCount = await CreatorAccessRequest.countDocuments({ status: "pending" });
    const approvedCount = await CreatorAccessRequest.countDocuments({ status: "approved" });
    const rejectedCount = await CreatorAccessRequest.countDocuments({ status: "rejected" });

    res.json({
      status: "success",
      data: {
        pending: pendingCount,
        approved: approvedCount,
        rejected: rejectedCount,
        total: pendingCount + approvedCount + rejectedCount,
      },
    });
  } catch (error) {
    console.error("Error fetching creator access stats:", error);
    res.status(500).json({
      status: "error",
      message: "Internal server error",
    });
  }
});

module.exports = adminCreatorAccessRouter; 