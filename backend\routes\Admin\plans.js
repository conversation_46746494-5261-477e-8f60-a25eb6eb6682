const express = require('express');
const router = express.Router();
const { VerifyTokenAuth } = require('../../middlewares/AdminAuth');
const {
  getAllPlans,
  createPlan,
  updatePlan,
  deletePlan
} = require('../../middlewares/brand/planController');

// Protect all plan routes with admin auth
router.use(VerifyTokenAuth);

// GET /api/admin/plans - Get all plans
router.get('/', getAllPlans);

// POST /api/admin/plans - Create new plan
router.post('/', createPlan);

// PATCH /api/admin/plans/:id - Update existing plan
router.patch('/:id', updatePlan);

// DELETE /api/admin/plans/:id - Delete plan (soft delete)
router.delete('/:id', deletePlan);

module.exports = router;
