const express = require("express"); // Auth middleware to protect admin routes
const { VerifyTokenAuth } = require("../../middlewares/AdminAuth"); // User model from database
const {
    User,
    appliedCampaigns,
    Campaign,
    campaignSubmission,
} = require("../../database");
const mongoose = require("mongoose");

const registered = express.Router();

registered.get("/", VerifyTokenAuth, async (req, res) => {
  try {
    // Pagination + limits
    const page = Math.max(parseInt(req.query.page) || 1, 1);
    const limit = Math.min(parseInt(req.query.limit) || 20, 200);
    const search = req.query.search?.trim() || "";
    const skip = (page - 1) * limit;

    // Country filter (comma-separated)
    const countries = req.query.country
      ? req.query.country.split(",").map((c) => c.trim())
      : [];

    // Base query
    let query = {};

    if (search) {
      query.$or = [
        { email: { $regex: search, $options: "i" } },
        { name: { $regex: search, $options: "i" } },
      ];
    }

    if (countries.length > 0) {
      if (countries.includes("(empty)")) {
        // Match users without a country OR in list
        query.$or = [
          ...(query.$or || []),
          { country: { $in: countries.filter((c) => c !== "(empty)") } },
          { $or: [{ country: { $exists: false } }, { country: "" }] },
        ];
      } else {
        query.country = { $in: countries };
      }
    }

    // Run both queries in parallel
    const [total, registered] = await Promise.all([
      User.countDocuments(query),
      User.find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .select("-password"),
    ]);

    res.json({
      status: "success",
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      registered,
    });
  } catch (error) {
    console.error("Error fetching registered users:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching registered users",
      error: error.message,
    });
  }
});


// Fetech User by id all destails here
registered.get("/:email", VerifyTokenAuth, async (req, res) => {
    try {
        const email = decodeURIComponent(req.params.email);

        const user = await User.findOne({ email });

        if (!user) {
            return res
                .status(404)
                .json({ status: "fail", message: "User not found" });
        }

        res.json({
            status: "success",
            user: {
                id: user._id,
                name: user.name,
                email: user.email,
                isVerified: user.isVerified,
                instagramId: user.instagramId,
                tiktokId: user.tiktokId,
                points: user.points,
                joinedAt: new Date(
                    user.createdAt.getTime() - 7 * 60 * 60 * 1000
                ).toISOString(),
                blocked: user.blocked,
            },
        });
    } catch (error) {
        console.error("Error fetching user by email:", error);
        res.status(500).json({
            status: "failed",
            message: "Something went wrong",
            error: error.message,
        });
    }
});

// GET /admin/users/:email/details
// Fetch detailed user information including campaign history
registered.get("/:id/details", VerifyTokenAuth, async (req, res) => {
    try {
        const { id } = req.params;

        const user = await User.findById(id);

        if (!user) {
            return res.status(404).json({
                status: "fail",
                message: "User not found",
            });
        }

        // Get applications
        const applications = await appliedCampaigns
            .find({ email: user.email })
            .populate("campaign", "campaignTitle brandName status")
            .sort({ createdAt: -1 })
            .lean();

        // Get submissions
        const submissions = await campaignSubmission
            .find({ email: user.email })
            .populate("campaign_id", "campaignTitle")
            .sort({ submitted_at: -1 })
            .lean();

        // Map submissions for quick lookup
        const submissionMap = {};
        submissions.forEach((sub) => {
            if (sub.campaign_id && sub.campaign_id._id) {
                submissionMap[sub.campaign_id._id.toString()] = sub;
            }
        });

        // Construct campaign history
        const campaignHistory = applications
            .filter((app) => app.campaign && app.campaign._id)
            .map((app) => {
                const submission = submissionMap[app.campaign._id.toString()];
                return {
                    campaignName: app.campaign.campaignTitle || "Unknown Campaign",
                    brand: app.campaign.brandName || "Unknown Brand",
                    joinDate: app.createdAt
                        ? new Date(app.createdAt).toISOString().split("T")[0]
                        : "N/A",
                    submitted: submission ? "Yes" : "No",
                    status: app.status || "N/A",
                    campaignId: app.campaign._id,
                };
            });

        // Use latest application for phone/address if available
        const latestApp = applications.length > 0 ? applications[0] : null;

        // Construct user details response
        const userDetails = {
            id: user._id,
            name: user.name || "Not Provided",
            email: user.email,
            signupDate: user.createdAt
                ? new Date(user.createdAt).toISOString().split("T")[0]
                : "N/A",
            approved: user.paid_status === "approved",
            locked: user.blocked || false,
            verifiedEmail: user.isVerified || false,
            blocked: user.blocked || false,
            instagramId: user.instagramId || "Not Provided",
            tiktokId: user.tiktokId || "Not Provided",
            phone: latestApp?.phone || "Not Provided",
            address:
                latestApp
                    ? [latestApp.address, latestApp.city, latestApp.state, latestApp.zipCode]
                          .filter(Boolean)
                          .join(", ") || "Not Provided"
                    : "Not Provided",
            campaignHistory,
        };

        return res.json({
            status: "success",
            user: userDetails,
        });
    } catch (error) {
        console.error("Error fetching user details:", error);
        return res.status(500).json({
            status: "failed",
            message: "Something went wrong",
            error: error.message,
        });
    }
});


// POST /admin/users/paginate/
// Alternate cursor pagination using POST body with LastId
registered.post("/paginate/", VerifyTokenAuth, async (req, res) => {
    try {
        const limit = 100;
        const { LastId } = req.body;
        let query = {};
        if (LastId) {
            // Convert string ID to ObjectId type for query
            query._id = { $gt: new mongoose.Types.ObjectId(LastId) };
        }

        const results = await User.find(query)
            .sort({ _id: 1 })
            .limit(limit + 1);

        const hasMore = results.length > limit;
        const limitedResults = hasMore ? results.slice(0, limit) : results;

        const users = limitedResults.map((item) => ({
            id: item._id,
            name: item.name,
            email: item.email,
            isVerified: item.isVerified,
            blocked: item.blocked,
            joinedAt: new Date(
                item.createdAt.getTime() - 7 * 60 * 60 * 1000
            ).toISOString(),
        }));

        res.json({
            status: "success",
            registered: users,
            nextCursor: hasMore
                ? limitedResults[limitedResults.length - 1]._id
                : null,
            isLastPage: !hasMore,
        });
    } catch (error) {
        console.error("Error paginating registered users:", error);
        res.status(500).json({
            status: "failed",
            message: "Something went wrong",
            error: error.message,
        });
    }
});

// DELETE /admin/users/:id
// Hard-delete a user by ID
registered.delete("/:id", VerifyTokenAuth, async (req, res) => {
    try {
        const { id } = req.params;
        const deleted = await User.findByIdAndDelete(id);
        if (!deleted) {
            return res.json({ status: "failed", message: "User not found" });
        }
        res.json({ status: "success", message: "User deleted" });
    } catch (error) {
        console.error("Error deleting user:", error);
        res.status(500).json({
            status: "failed",
            message: "Something went wrong",
            error: error.message,
        });
    }
});

// PATCH /admin/users/:id/block
// Toggle blocked status of a user
registered.patch("/:id/block", VerifyTokenAuth, async (req, res) => {
    try {
        const { id } = req.params;
        const user = await User.findById(id);
        if (!user) {
            return res.json({ status: "failed", message: "User not found" });
        }
        user.blocked = !user.blocked;
        await user.save();
        res.json({
            status: "success",
            message: `User ${user.blocked ? "blocked" : "unblocked"}`,
        });
    } catch (error) {
        console.error("Error toggling block status:", error);
        res.status(500).json({
            status: "failed",
            message: "Something went wrong",
            error: error.message,
        });
    }
});

// PATCH /admin/users/:id/verify
// Update isVerified status manually
registered.patch("/:id/verify", VerifyTokenAuth, async (req, res) => {
    try {
        const { id } = req.params;
        const { isVerified } = req.body;

        if (typeof isVerified !== "boolean") {
            return res.status(400).json({
                status: "failed",
                message: "Invalid value for isVerified",
            });
        }

        const user = await User.findById(id);
        if (!user) {
            return res
                .status(404)
                .json({ status: "failed", message: "User not found" });
        }

        user.isVerified = isVerified;
        await user.save();

        res.json({
            status: "success",
            message: `User email verification set to ${
                isVerified ? "Yes" : "No"
            }`,
        });
    } catch (error) {
        console.error("Error updating verification status:", error);
        res.status(500).json({
            status: "failed",
            message: "Something went wrong",
            error: error.message,
        });
    }
});

module.exports = {
    registered,
};
