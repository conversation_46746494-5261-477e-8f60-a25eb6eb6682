// 📁 File: routes/admin/rewardsAdmin.js

const express = require("express");
const { VerifyTokenAuth } = require("../../middlewares/AdminAuth");
const {
  User,
  rewardTransaction,
  pointRule,
  pointEventLog,
  rewardTier,
} = require("../../database");

const adminRouter = express.Router();

// 🔹 GET all users with profile + editable points, sorted by points desc
adminRouter.get("/points/users", VerifyTokenAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1; // default page 1
    const limit = parseInt(req.query.limit) || 10; // default 10 users per page
      const search = req.query.search || '';
    const skip = (page - 1) * limit;

    // Build search filter
    const filter = search
      ? {
          $or: [
            { name: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } }
          ]
        }
      : {};

    const totalUsers = await User.countDocuments(filter);
    const users = await User.find({}, "email firstName lastName name points createdAt")
      .sort({ points: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const formatted = users.map((user) => ({
      _id: user._id,
      email: user.email,
      name: user.name || `${user.firstName || ""} ${user.lastName || ""}`.trim(),
      points: user.points || 0,
      joinedAt: user.createdAt,
    }));

    res.json({
      status: "success",
      users: formatted,
      totalUsers,
      totalPages: Math.ceil(totalUsers / limit),
      currentPage: page,
    });
  } catch (err) {
    console.error("Error fetching users with points:", err);
    res.status(500).json({ status: "failed", message: "Server error" });
  }
});


// 🔹 PATCH user points (update exact points)
adminRouter.patch("/points/user/:id", VerifyTokenAuth, async (req, res) => {
  const { id } = req.params;
  const { points, note } = req.body;

  if (typeof points !== "number") {
    return res.status(400).json({ status: "failed", message: "Points must be a number" });
  }

  try {
    const user = await User.findByIdAndUpdate(id, { points }, { new: true });

    if (!user) {
      return res.status(404).json({ status: "failed", message: "User not found" });
    }

    await pointEventLog.create({
      user: id,
      source: "manual",
      points,
      note: note || "Manual points update by admin",
    });

    res.json({ status: "success", message: "Points updated successfully", user });
  } catch (err) {
    console.error("Error updating user points:", err);
    res.status(500).json({ status: "failed", message: "Server error" });
  }
});


// 🔹 GET all point event logs
adminRouter.get("/points/logs", VerifyTokenAuth, async (req, res) => {
  try {
    const logs = await pointEventLog
      .find()
      .populate("user", "email") // populate user email
      .sort({ createdAt: -1 })
      .lean();

    const formatted = logs.map((log) => ({
      user: log.user?.email || "Unknown",
      points: log.points,
      source: log.source,
      note: log.note || "",
      createdAt: log.createdAt,
    }));

    res.json({ status: "success", data: formatted });
  } catch (err) {
    console.error("Error fetching point logs:", err);
    res.status(500).json({ status: "failed", message: "Server error" });
  }
});


// GET /admin/rewards/redemptions
adminRouter.get("/rewards/redemptions", VerifyTokenAuth, async (req, res) => {
  try {
    const redemptions = await rewardTransaction
      .find({})
      .populate("user", "email name")
      .populate("rewardTier", "rewardLabel pointsRequired")
      .sort({ createdAt: -1 })
      .lean();

    const formatted = redemptions.map(r => ({
      id: r._id,
      user: r.user?.name || "Unknown",
      email: r.user?.email || "Unknown",
      reward: r.rewardTier?.rewardLabel || "Deleted Tier",
      pointsUsed: r.rewardTier?.pointsRequired || 0,
      status: r.status,
      requestedAt: r.createdAt,
      adminNote: r.adminNote || ""
    }));

    return res.json({ status: "success", redemptions: formatted });
  } catch (err) {
    console.error("❌ Error fetching redemptions:", err);
    return res.status(500).json({ status: "failed", message: "Server error" });
  }
});

// PATCH /admin/rewards/redemptions/:id/complete
adminRouter.patch("/rewards/redemptions/:id/complete", VerifyTokenAuth, async (req, res) => {
  const { id } = req.params;
  const { adminNote } = req.body;

  try {
    const transaction = await rewardTransaction.findById(id);
    if (!transaction) {
      return res.status(404).json({ status: "failed", message: "Transaction not found" });
    }

    if (transaction.status === "completed") {
      return res.json({ status: "failed", message: "Already marked as completed" });
    }

    transaction.status = "completed";
    transaction.adminNote = adminNote || "";
    await transaction.save();

    return res.json({ status: "success", message: "Reward marked as completed" });
  } catch (err) {
    console.error("❌ Error updating redemption:", err);
    return res.status(500).json({ status: "failed", message: "Server error" });
  }
});


module.exports = { adminRouter };
