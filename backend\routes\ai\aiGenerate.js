const express = require('express');
const router = express.Router();
const aiGenerateService = require('../../services/ai/aiGenerateService');

// POST /api/ai/generate-campaign
router.post('/generate-campaign', async (req, res) => {
  try {
    const { productURL } = req.body;

    if (!productURL) {
      return res.status(400).json({
        status: 'failed',
        message: 'Product URL is required'
      });
    }

    // Validate URL format
    try {
      new URL(productURL);
    } catch (error) {
      return res.status(400).json({
        status: 'failed',
        message: 'Invalid URL format'
      });
    }

    console.log('🤖 Starting AI campaign generation for URL:', productURL);

    // Generate campaign data from URL
    const campaignData = await aiGenerateService.generateCampaignFromURL(productURL);

    console.log('✅ AI campaign generation completed successfully');

    res.status(200).json({
      status: 'success',
      message: 'AI Draft completed. All available fields have been pre-filled. Please review and edit as needed.',
      data: campaignData
    });

  } catch (error) {
    console.error('❌ Error in AI generate campaign:', error);
    
    res.status(500).json({
      status: 'failed',
      message: 'Failed to generate campaign content. Please try again or enter the information manually.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/ai/health
router.get('/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'AI Generate service is running',
    timestamp: new Date().toISOString()
  });
});

module.exports = router; 