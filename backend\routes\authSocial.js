
const { VerifyToken } = require("../middlewares/auth");
// routes/socialAuth.js
const express = require("express");
const axios = require("axios");
const {User} = require("../database");
const { encrypt, decrypt } = require("../utils/secure"); // optional encryption util

const router = express.Router();

/**
 * @route POST /social/connect
 * @desc Save OAuth tokens + fetch user stats
 * @body { provider: 'instagram' | 'tiktok', accessToken: string }
 */
router.get("/connect", VerifyToken, async (req, res) => {
  try {
    const { provider, accessToken } = req.body;
    if (!["instagram", "tiktok"].includes(provider)) {
      return res.status(400).json({ status: "error", message: "Invalid provider" });
    }

    // Fetch profile data from the provider API
    let profileData = {};
    if (provider === "instagram") {
      // Example: call Instagram Graph API
      const igRes = await axios.get(
        `https://graph.instagram.com/me?fields=id,username,media_count&access_token=${accessToken}`
      );
      profileData = {
        accessToken: encrypt(accessToken),
        username: igRes.data.username,
        postsCount: igRes.data.media_count,
      };
    } else if (provider === "tiktok") {
      // Example TikTok API call (adjust for their API)
      const tkRes = await axios.get(
        `https://open.tiktokapis.com/v2/user/info/`,
        { headers: { Authorization: `Bearer ${accessToken}` } }
      );
      profileData = {
        accessToken: encrypt(accessToken),
        username: tkRes.data.data.username,
        followerCount: tkRes.data.data.follower_count,
      };
    }

    // Save into user record
    const user = await User.findById(req.user.id);
    user.sns[provider] = profileData;
    user.snsConnected = true;
    await user.save();

    res.json({ status: "success", user });
  } catch (err) {
    console.error("Social connect error:", err.response?.data || err.message);
    res.status(500).json({ status: "error", message: "Failed to connect account" });
  }
});

/**
 * @route GET /social/status
 * @desc Get connected social accounts
 */
router.get("/status", VerifyToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select("sns snsConnected");
    res.json({ status: "success", sns: user.sns, snsConnected: user.snsConnected });
  } catch (err) {
    res.status(500).json({ status: "error", message: "Failed to fetch status" });
  }
});

module.exports = router;
