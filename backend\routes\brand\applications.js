const express = require("express");
const router = express.Router();
const { verifyBrandToken } = require("../../middlewares/brand/authController");
const {
  getBrandApplicants,
  updateBrandApplicantStatus,
  deleteBrandApplicant,
} = require("../../middlewares/brand/brandApplicationController");

router.get("/:campaignId", verifyBrandToken, getBrandApplicants);
router.patch("/:applicantId/status", verifyBrandToken, updateBrandApplicantStatus);
router.delete("/:campaignId/:applicantId", verifyBrandToken, deleteBrandApplicant);

module.exports = router;
