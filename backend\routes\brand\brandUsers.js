const express = require("express");
const mongoose = require("mongoose");
const { User } = require("../../database");
const { verifyBrandToken } = require("../../middlewares/brand/authController");

const brandUsers = express.Router();

// GET /brand/users?cursor=...
brandUsers.get("/", verifyBrandToken, async (req, res) => {
  try {
    const limit = 500;
    const cursor = req.query.cursor;
    let query = {};
    if (cursor) {
      query._id = { $gt: cursor };
    }

    const results = await User.find(query).sort({ _id: 1 }).limit(limit + 1);
    const hasMore = results.length > limit;
    const limitedResults = hasMore ? results.slice(0, limit) : results;

    const users = limitedResults.map((item) => ({
      id: item._id,
      name: item.name,
      email: item.email,
      isVerified: item.isVerified,
      joinedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
      blocked: item.blocked
    }));

    res.json({
      status: "success",
      registered: users,
      nextCursor: hasMore ? limitedResults[limitedResults.length - 1]._id : null,
      isLastPage: !hasMore,
    });
  } catch (error) {
    res.status(500).json({ status: "failed", message: "Something went wrong", error: error.message });
  }
});

// GET /brand/users/:email
brandUsers.get("/:email", verifyBrandToken, async (req, res) => {
  try {
    const email = decodeURIComponent(req.params.email);
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ status: "fail", message: "User not found" });
    }

    res.json({
      status: "success",
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        isVerified: user.isVerified,
        instagramId: user.instagramId,
        tiktokId: user.tiktokId,
        points: user.points,
        joinedAt: new Date(user.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
        blocked: user.blocked,
      }
    });
  } catch (error) {
    res.status(500).json({ status: "failed", message: "Something went wrong", error: error.message });
  }
});

// POST /brand/users/paginate/
brandUsers.post("/paginate/", verifyBrandToken, async (req, res) => {
  try {
    const limit = 100;
    const { LastId } = req.body;
    let query = {};
    if (LastId) {
      query._id = { $gt: new mongoose.Types.ObjectId(LastId) };
    }

    const results = await User.find(query).sort({ _id: 1 }).limit(limit + 1);
    const hasMore = results.length > limit;
    const limitedResults = hasMore ? results.slice(0, limit) : results;

    const users = limitedResults.map((item) => ({
      id: item._id,
      name: item.name,
      email: item.email,
      isVerified: item.isVerified,
      blocked: item.blocked,
      joinedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
    }));

    res.json({
      status: "success",
      registered: users,
      nextCursor: hasMore ? limitedResults[limitedResults.length - 1]._id : null,
      isLastPage: !hasMore,
    });
  } catch (error) {
    res.status(500).json({ status: "failed", message: "Something went wrong", error: error.message });
  }
});

// PATCH /brand/users/:id/block
brandUsers.patch("/:id/block", verifyBrandToken, async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findById(id);
    if (!user) {
      return res.json({ status: "failed", message: "User not found" });
    }
    user.blocked = !user.blocked;
    await user.save();
    res.json({ status: "success", message: `User ${user.blocked ? 'blocked' : 'unblocked'}` });
  } catch (error) {
    res.status(500).json({ status: "failed", message: "Something went wrong", error: error.message });
  }
});

// PATCH /brand/users/:id/verify
brandUsers.patch("/:id/verify", verifyBrandToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { isVerified } = req.body;

    if (typeof isVerified !== 'boolean') {
      return res.status(400).json({ status: "failed", message: "Invalid value for isVerified" });
    }

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({ status: "failed", message: "User not found" });
    }

    user.isVerified = isVerified;
    await user.save();

    res.json({ status: "success", message: `User email verification set to ${isVerified ? 'Yes' : 'No'}` });
  } catch (error) {
    res.status(500).json({ status: "failed", message: "Something went wrong", error: error.message });
  }
});

module.exports = { brandUsers };
