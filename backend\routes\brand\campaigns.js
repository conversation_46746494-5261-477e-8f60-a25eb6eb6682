const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");
const { Campaign, ExtensionRequest, User, BrandCampaignRequest } = require("../../database");
const mongoose = require("mongoose");
const { scrapeProductPage } = require('../../utils/scraper');
const { generatePrompt, callOpenAI, mapResponseToCampaign, classifyCategory } = require('../../utils/aiUtils');
const { verifyBrandToken } = require("../../middlewares/brand/authController");

const brandCampaignRouter = express.Router();

// GET /api/brand/campaigns - Get all campaigns for the authenticated brand
brandCampaignRouter.get("/", async (req, res) => {
  try {
    // For now, return mock data since we don't have brand-specific campaigns yet
    // In a real implementation, you'd filter by brand/user ID
    const mockCampaigns = await Campaign.find();

    res.json({
      status: "success",
      campaigns: mockCampaigns
    });
  } catch (error) {
    console.error("Error fetching brand campaigns:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch campaigns"
    });
  }
});

// GET /api/brand/campaigns/extension-requests - Get all pending extension requests for the brand's campaigns
// ⚠️ IMPORTANT: This route MUST come before /:id to prevent route conflicts
brandCampaignRouter.get("/extension-requests", async (req, res) => {
  try {
    console.log("extension-requests");
    // TODO: Replace with actual brand authentication and campaign filtering
    // For now, fetch all pending extension requests and populate creator/campaign info
    const requests = await ExtensionRequest.find({ status: 'pending' })
      .populate({ path: 'creator', select: 'name' })
      .populate({ path: 'campaign', select: 'campaignTitle' });

    let formatted;
    
    // If no requests found, send a dummy request
    if (requests.length === 0) {
      formatted = [];
    } else {
      formatted = requests.map(req => ({
        id: req._id,
        creatorName: req.creator?.name || '',
        campaignTitle: req.campaign?.campaignTitle || '',
        daysRequested: req.daysRequested,
        reason: req.reason,
        status: req.status
      }));
    }

    res.json({ status: 'success', requests: formatted });
  } catch (error) {
    console.error('Error fetching extension requests:', error);
    res.status(500).json({ status: 'failed', message: 'Failed to fetch extension requests' });
  }
});

// GET /api/brand/campaigns/:id - Get specific campaign details
brandCampaignRouter.get("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    
    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: "failed",
        message: "Invalid campaign ID"
      });
    }

    // First try to find in Campaign collection
    let campaign = await Campaign.findById(id);
    console.log("Looking for campaign ID:", id);
    console.log("Found in Campaign collection:", campaign ? "Yes" : "No");

    // If not found in Campaign collection, try BrandCampaignRequest collection
    if (!campaign) {
      console.log("Trying BrandCampaignRequest collection...");
      campaign = await BrandCampaignRequest.findById(id);
      console.log("Found in BrandCampaignRequest collection:", campaign ? "Yes" : "No");
    }

    console.log("Found campaign:", campaign);
    
    if (!campaign) {
      return res.status(404).json({
        status: "failed",
        message: "Campaign not found"
      });
    }

    res.status(200).json({
      status: "success",
      campaign
    });
  } catch (error) {
    console.error("Error fetching campaign:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch campaign"
    });
  }
});

// DELETE /api/brand/campaigns/:id - Delete a draft campaign
brandCampaignRouter.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    
    // Mock check - only allow deletion of draft campaigns
    const mockCampaigns = {
      1: { status: 'active' },
      2: { status: 'draft' },
      3: { status: 'completed' },
      4: { status: 'active' },
      5: { status: 'draft' },
      6: { status: 'completed' }
    };

    const campaign = mockCampaigns[id];
    if (!campaign) {
      return res.json({
        status: "failed",
        message: "Campaign not found"
      });
    }

    if (campaign.status !== 'draft') {
      return res.json({
        status: "failed",
        message: "Only draft campaigns can be deleted"
      });
    }

    // In a real implementation, you would delete from database here
    // await Campaign.findByIdAndDelete(id);

    res.json({
      status: "success",
      message: "Campaign deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting campaign:", error);
    res.json({
      status: "failed",
      message: "Failed to delete campaign"
    });
  }
});


brandCampaignRouter.post("/generate-campaign-draft", async (req, res) => {
  const { url} = req.body;

  if (!url) {
    return res.status(400).json({ status: false, message: "Product URL is required." });
  }

  try {
    console.log(`🔍 Scraping URL: ${url}`);
    const scrapedData = await scrapeProductPage(url);
    console.log("✅ Scraped Data:", scrapedData);

    const finalCategory = classifyCategory(scrapedData);
    console.log(`📦 Detected Category: ${finalCategory}`);

    const {prompt, category} = generatePrompt(scrapedData, finalCategory);
   // console.log("📝 Generated Prompt:", prompt);

    let aiResponse;
      console.log("🚀 Using OpenAI GPT Model");
      aiResponse = await callOpenAI(prompt);
   

  //  console.log("🤖 AI Response:", aiResponse);

    const campaignData = mapResponseToCampaign(aiResponse, category, scrapedData);

    res.json({ status: true, campaign: campaignData });

  } catch (err) {
    console.error("❌ Error generating campaign draft:", err);
    res.status(500).json({
      status: false,
      message: err.message || "Failed to generate campaign draft."
    });
  }
});



// POST /api/campaigns/invite/:campaignId
brandCampaignRouter.post("/invite/:campaignId", verifyBrandToken, async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { creatorId } = req.body;

    if (!creatorId) {
      return res.status(400).json({ status: "error", message: "creatorId is required" });
    }

    // Find campaign
    const campaign = await Campaign.findById(campaignId);
    if (!campaign) {
      return res.status(404).json({ status: "error", message: "Campaign not found" });
    }

    // Find creator
    const creator = await User.findById(creatorId);
    if (!creator) {
      return res.status(404).json({ status: "error", message: "Creator not found" });
    }

    // Prevent duplicate invitations
    if (campaign.invitedCreators?.includes(creator._id)) {
      return res.status(400).json({ status: "error", message: "Creator already invited" });
    }

    // Add creator to campaign's invited list
    campaign.invitedCreators = campaign.invitedCreators || [];
    campaign.invitedCreators.push(creator._id);
    await campaign.save();

    // Optionally: update creator's campaign invitations
    creator.appliedCampaigns = creator.appliedCampaigns || [];
    if (!creator.appliedCampaigns.includes(campaign._id)) {
      creator.appliedCampaigns.push(campaign._id);
      await creator.save();
    }

    res.json({ status: "success", message: `Creator ${creator.name} invited to campaign ${campaign.campaignTitle}` });
  } catch (error) {
    console.error(error);
    res.status(500).json({ status: "error", message: "Server error" });
  }
});



module.exports = brandCampaignRouter;
