const express = require("express");
const { verifyBrandToken } = require("../../middlewares/brand/authController");
const { sendTrackingEmailToUser, sendApprovalEmail, sendRejectionEmail } = require("../../functions/sendEmail");
const PerformanceService = require("../../services/performanceService");
const { appliedCampaigns, campaignSubmission, User, Campaign, BlockedCreator, ExtensionRequest } = require("../../database");

const { registerTracking } = require('../../services/trackingService');
const sendEmail = require("../../utils/sendEmail");
const brandCreatorRouter = express.Router();


// Block creator permanently for this brand
brandCreatorRouter.post('/block-creator', verifyBrandToken, async (req, res) => {
  const {creatorId} = req.body;
  const brandId = req.user?._id; // Assuming brand ID is available in req.brand
  console.log("brandId", brandId)
  if (!brandId || !creatorId) {
    return res.status(400).json({ status: 'failed', message: 'Brand ID and Creator ID are required' });
  }

  try {
    // Check if already blocked
    const alreadyBlocked = await BlockedCreator.findOne({ brandId, creatorId });
    if (alreadyBlocked) {
      return res.status(200).json({ status: 'success', message: 'Creator is already blocked' });
    }

    // Save block info
    await BlockedCreator.create({ brandId, creatorId });

   return res.status(200).json({ status: 'success', message: 'Creator blocked successfully' });
  } catch (error) {
    console.error('Error blocking creator:', error);
   return res.status(500).json({ status: 'failed', message: 'Failed to block creator' });
  }
});

// Unblock creator for this brand
brandCreatorRouter.post('/unblock-creator', verifyBrandToken, async (req, res) => {
  const {creatorId } = req.body;
  const brandId = req.user?._id; // Assuming brand ID is available in req.brand

  if (!brandId || !creatorId) {
    return res.status(400).json({ status: 'failed', message: 'Brand ID and Creator ID are required' });
  }

  try {
    const blockedRecord = await BlockedCreator.findOne({ brandId, creatorId });

    if (!blockedRecord) {
      return res.status(404).json({ status: 'failed', message: 'Creator is not blocked' });
    }

    await BlockedCreator.deleteOne({ brandId, creatorId });

   return res.status(200).json({ status: 'success', message: 'Creator unblocked successfully' });
  } catch (error) {
    console.error('Error unblocking creator:', error);
    return res.status(500).json({ status: 'failed', message: 'Failed to unblock creator' });
  }
});


// Get blocked creators for the logged-in brand
brandCreatorRouter.get('/blocked-creators', verifyBrandToken, async (req, res) => {
  try {
    const brandId = req.user._id;

    // Find blocked creators for this brand
    const blockedRecords = await BlockedCreator.find({ brandId }).populate('creatorId', 'name email instagramId tiktokId');

    // Map to frontend-friendly data
    const blockedCreators = blockedRecords.map(bc => ({
      id: bc._id,
      creatorId: bc.creatorId._id,
      name: bc.creatorId.name,
      email: bc.creatorId.email,
      instagramId: bc.creatorId.instagramId,
      tiktokId: bc.creatorId.tiktokId,
    }));

    res.status(200).json({ status: 'success', data: blockedCreators });
  } catch (err) {
    console.error('Error fetching blocked creators:', err);
    res.status(500).json({ status: 'failed', message: 'Failed to fetch blocked creators' });
  }
});
// GET /api/brand/creators/:campaignId - Get creators for a specific campaign
brandCreatorRouter.get("/:campaignId", verifyBrandToken, async (req, res) => {
  try {
    const { campaignId } = req.params;
    const brandId = req.user?._id; // Assuming brand ID is available in req.brand
   
    // Fetch real creator applications from database
    let applications = await appliedCampaigns.find({ 
      campaign: campaignId 
    }).populate('campaign', 'campaignTitle campaignType pricingModel minBid maxBid');

    // Fetch content submissions for this campaign
    const submissions = await campaignSubmission.find({ 
      campaign_id: campaignId 
    });

     const campaignExist = await Campaign.findById(campaignId);
    if (!campaignExist) {
      return res.json({ status: "failed", message: "Campaign not found" });
    }


    // Fetch blocked creators for this brand and build a fast lookup set
    const blockedRecords = await BlockedCreator.find({ brandId }).select('creatorId');
    const blockedCreatorIds = new Set(blockedRecords.map(b => b.creatorId.toString()));

     

    const approvalLimit = campaignExist.recurring || campaignExist.creatorCount || 0;
    const maxVisibleCreators = approvalLimit + 5;
 // Count approved creators
    const approvedCreators = applications.filter(app => app.status === "Approved");

    // Case 1: If approval limit is reached or exceeded
    if (approvedCreators.length >= approvalLimit && campaignExist.recruiting !="-1") {
      applications = approvedCreators;
    } else {
      // Case 2: Not reached limit → include all, but apply locking on ones beyond limit + 5
      applications.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt)); // oldest first
    }

    // Transform data to match frontend expectations
    const creators = await Promise.all(applications.map(async (app, index) => {
      // Find corresponding submission
      const submission = submissions.find(sub => sub.email === app.email);
      // Determine platform based on social IDs
      const platform = app.tiktokId ? 'TikTok' : app.instagramId ? 'Instagram' : 'Unknown';
      const socialId = app.tiktokId || app.instagramId || '';
      const profileUrl = app.tiktokId 
        ? `https://tiktok.com/@${app.tiktokId.replace('@', '')}`
        : app.instagramId 
        ? `https://instagram.com/${app.instagramId.replace('@', '')}`
        : '';

        
      // Determine content status
      let contentStatus = 'Not Submitted';
      if (submission) {
        if (submission.content_status === 'Approved') {
          contentStatus = '✅ Submitted';
        } else if (submission.content_status === 'Rejected') {
          contentStatus = '❌ Rejected';
        } else if (submission.instagram_urls?.length > 0 || submission.tiktok_urls?.length > 0) {
          contentStatus = '📝 Pending Review';
        }
      }

     // console.log("contentStatus", contentStatus)

      // Fetch the latest extension request for this creator and campaign
      let extensionStatus = null;
      
      // Find user ID from email since appliedCampaigns doesn't have user field
      const user = await User.findOne({ email: app.email });
      if (user) {
        const latestExtension = await ExtensionRequest.findOne({
          creator: user._id, // Use user ID from email lookup
          campaign: campaignId
        }).sort({ createdAt: -1 });

        if (latestExtension) {
          if (latestExtension.status === 'pending') extensionStatus = `⏳ +${latestExtension.daysRequested}d Pending`;
          else if (latestExtension.status === 'approved') extensionStatus = `✅ +${latestExtension.daysRequested}d Approved`;
          else if (latestExtension.status === 'rejected') extensionStatus = `❌ Rejected`;
        }
      }

       const isApproved = app.status === "Approved";
        const isLocked =
          !isApproved &&
          approvedCreators.length < approvalLimit &&
          index >= maxVisibleCreators;

  // Check block status via Set
      const blocked =  user ? blockedCreatorIds.has(user._id.toString()) : false;


      return {
        id: app._id,
        userid : user ? user._id : null, // Use user ID from email lookup
        campaignId: app.campaign,
        creatorId: app._id,
        name: app.name,
        email: app.email,
        socialId: socialId,
        platform: platform,
        profileUrl: profileUrl,
        performanceScore: null, // TODO: Calculate from performance service
        tracking: submission?.tracking_info?.tracking_number || '',
        deliveryStatus: submission?.tracking_info?.delivery_status || '',
        tracking_info: submission?.tracking_info || {},
        deliveredAt: submission?.tracking_info?.delivery_status === 'Delivered' ? submission?.tracking_info?.last_updated : null || null,
        contentStatus: contentStatus,
        extensionStatus: extensionStatus,
        participationStatus: app.status,
        appliedAt: app.createdAt,
        bid: app.bid,
        approvedAt: app.status === 'Approved' ? app.updatedAt : null,
        shippingInfo: {
          name: app.name,
          phone: app.phone,
          address: `${app.address}, ${app.city}, ${app.state} ${app.zipCode}, ${app.country}`
        },
        contentUrls: {
          instagram: submission?.instagram_urls?.length > 0 ? submission?.instagram_urls : null,
          tiktok: submission?.tiktok_urls?.length > 0 ? submission?.tiktok_urls : null
        },
        isLocked,
        blocked: blocked,
      };
    }));

    res.json({
      status: "success",
      data: creators
    });
  } catch (error) {
    console.error("Error fetching creators:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch creators"
    });
  }
});

// PUT /api/brand/creators/:creatorId/tracking - Update tracking information
brandCreatorRouter.put("/:creatorId/tracking", verifyBrandToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { trackingNumber, courier } = req.body;

    // Validate input
    if (!trackingNumber || trackingNumber.length > 50) {
      return res.json({
        status: "failed",
        message: "Tracking number is required and must be less than 50 characters"
      });
    }

    // Find the creator application
    const application = await appliedCampaigns.findById(creatorId);
    if (!application) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    // Find or create submission for this creator
    let submission = await campaignSubmission.findOne({
      campaign_id: application.campaign,
      email: application.email
    });

    if (!submission) {
      // Create new submission if it doesn't exist
      submission = new campaignSubmission({
        campaign_id: application.campaign,
        user_id: application._id, // Using application ID as user ID for now
        email: application.email,
        tracking_info: {
          courier: courier || 'Other',
          tracking_number: trackingNumber,
          tracking_link: `https://www.17track.net/en/track?nums=${trackingNumber}`,
          last_updated: new Date()
        }
      });
    } else {
      // Update existing submission
      submission.tracking_info = {
        courier: courier || 'Other',
        tracking_number: trackingNumber,
        tracking_link: `https://www.17track.net/en/track?nums=${trackingNumber}`,
        last_updated: new Date()
      };
    }

    await submission.save();
  // Register tracking number with 17TRACK
    try {
     const response = await registerTracking(trackingNumber);
     console.log("track data", response)
    } catch (trackError) {
      console.warn("⚠️  Failed to register tracking with 17TRACK:", trackError.message);
    }
  
      sendEmail(
    {
      userName: application.name,
      campaignTitle:  application.campaign?.campaignTitle,
      trackingLink: 'https://t.17track.net/en#nums='+trackingNumber,
      templateName: 'product_shipped',
      subject: 'Your product has shipped!',
      to: application.email
    })

    // Update performance score for tracking input
    // try {
    //   await PerformanceService.onTrackingInput(application._id, application.campaign);
    // } catch (performanceError) {
    //   console.error("Failed to update performance score:", performanceError);
    //   // Don't fail the request if performance update fails
    // }

    res.json({
      status: "success",
      message: "Tracking information updated and email sent",
      data: {
        id: application._id,
        tracking: trackingNumber,
        courier: courier || 'Other'
      }
    });
  } catch (error) {
    console.error("Error updating tracking:", error);
    res.json({
      status: "failed",
      message: "Failed to update tracking information"
    });
  }
});

// PUT /api/brand/creators/:creatorId/status - Update creator application status
brandCreatorRouter.put("/:creatorId/status", verifyBrandToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['Applied', 'Approved', 'Rejected', 'Not Submitted', 'Submitted'];
    if (!validStatuses.includes(status)) {
      return res.json({
        status: "failed",
        message: "Invalid status. Must be one of: " + validStatuses.join(', ')
      });
    }

    // Find the creator application
    const application = await appliedCampaigns.findById(creatorId);
    if (!application) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    // Update status
    application.status = status;
    await application.save();

    // Send appropriate email based on status change
    try {
      if (status === 'Approved') {
        await sendApprovalEmail(
          application.email,
          application.name,
          `Campaign #${application.campaign?.campaignTitle || application.campaign}` // Replace with actual campaign title
        );
      } else if (status === 'Rejected') {
        await sendRejectionEmail(
          application.email,
          application.name,
          `Campaign #${application.campaign?.campaignTitle || application.campaign}` // Replace with actual campaign title
        );
      }
    } catch (emailError) {
      console.error("Failed to send status email:", emailError);
      // Don't fail the request if email fails
    }

    // Update performance score for status change
    try {
      await PerformanceService.onStatusChange(application._id, application.campaign, status);
    } catch (performanceError) {
      console.error("Failed to update performance score:", performanceError);
      // Don't fail the request if performance update fails
    }

    res.json({
      status: "success",
      message: `Creator status updated to ${status}${status === 'Approved' || status === 'Rejected' ? ' and email sent' : ''}`,
      data: {
        id: application._id,
        participationStatus: status
      }
    });
  } catch (error) {
    console.error("Error updating creator status:", error);
    res.json({
      status: "failed",
      message: "Failed to update creator status"
    });
  }
});

// PUT /api/brand/creators/:creatorId/participation - Update participation status
brandCreatorRouter.put("/:creatorId/participation", verifyBrandToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { action } = req.body; // 'approve' or 'reject'

    // Validate action
    if (!['approve', 'reject'].includes(action)) {
      return res.json({
        status: "failed",
        message: "Invalid action. Must be 'approve' or 'reject'"
      });
    }

    // Find the creator application
    const application = await appliedCampaigns.findById(creatorId);
    if (!application) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    // Update participation status
    application.status = action === 'approve' ? 'Approved' : 'Rejected';
    await application.save();

    res.json({
      status: "success",
      message: `Participation ${action}d successfully`,
      data: {
        id: application._id,
        participationStatus: application.status
      }
    });
  } catch (error) {
    console.error("Error updating participation:", error);
    res.json({
      status: "failed",
      message: "Failed to update participation status"
    });
  }
});

// PUT /api/brand/creators/:creatorId/extension - Handle extension requests
brandCreatorRouter.put("/:creatorId/extension", verifyBrandToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { action } = req.body; // 'approve' or 'reject'

    // Validate action
    if (!['approve', 'reject'].includes(action)) {
      return res.json({
        status: "failed",
        message: "Invalid action. Must be 'approve' or 'reject'"
      });
    }

    // Find the creator application to get campaign ID
    const application = await appliedCampaigns.findById(creatorId);
    if (!application) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    // Find the user by email
    const user = await User.findOne({ email: application.email });
    if (!user) {
      return res.json({
        status: "failed",
        message: "User not found"
      });
    }

    // Find the latest pending extension request for this creator and campaign
    const extensionRequest = await ExtensionRequest.findOne({
      creator: user._id, // Use user ID, not application ID
      campaign: application.campaign,
      status: 'pending'
    }).sort({ createdAt: -1 });

    if (!extensionRequest) {
      return res.json({
        status: "failed",
        message: "No pending extension request found for this creator"
      });
    }

    // Update extension request status
    extensionRequest.status = action === 'approve' ? 'approved' : 'rejected';
    extensionRequest.decisionBy = req.brand?._id || null; // Assuming brand ID is available in req.brand
    extensionRequest.decisionAt = new Date();
    await extensionRequest.save();
   const campaign = await Campaign.findById(application.campaign);
    // Send email notification to creator about extension decision
    try {
      if (action === 'approve') {
        // TODO: Send approval email
         sendEmail( {
      userName: application.name,
      campaignTitle: campaign.campaignTitle,
      newDeadline: new Date(Date.now() + extensionRequest.daysRequested * 24 * 60 * 60 * 1000),
      templateName: 'extension_approved',
      subject: 'Your extension request was approved',
      to: application.email
    })
        console.log(`Extension approved for creator ${application.email}`);
      } else {
        // TODO: Send rejection email
        sendEmail({
      userName: application.name,
      campaignTitle: campaign.campaignTitle,
      templateName: 'extension_rejected',
      subject: 'Your extension request',
      to: application.email
    })
        console.log(`Extension rejected for creator ${application.email}`);
      }
    } catch (emailError) {
      console.error("Failed to send extension decision email:", emailError);
      // Don't fail the request if email fails
    }

    res.json({
      status: "success",
      message: `Extension ${action}d successfully`,
      data: {
        id: application._id,
        extensionStatus: action === 'approve' 
          ? `✅ +${extensionRequest.daysRequested}d Approved`
          : '❌ Rejected'
      }
    });
  } catch (error) {
    console.error("Error handling extension:", error);
    res.json({
      status: "failed",
      message: "Failed to update extension status"
    });
  }
});

// PUT /api/brand/creators/:creatorId/content-status - Update content status
brandCreatorRouter.put("/:creatorId/content-status", verifyBrandToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { status } = req.body;

    const validStatuses = ['Pending', 'Approved', 'Rejected'];
    if (!validStatuses.includes(status)) {
      return res.json({
        status: "failed",
        message: "Invalid status. Must be one of: " + validStatuses.join(', ')
      });
    }

    const application = await appliedCampaigns.findById(creatorId);
    if (!application) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    const updatedSubmission = await campaignSubmission.findOneAndUpdate(
      {
        campaign_id: application.campaign,
        email: application.email
      },
      {
        $set: { content_status: status }
      },
      { new: true } // returns the updated document
    );

    const campaign = await Campaign.findById(application.campaign);


   

    if (!updatedSubmission) {
      return res.json({
        status: "failed",
        message: "No content submission found for this creator"
      });
    }

     if(status == "Approved"){
       sendEmail({
      userName: application.name,
      campaignTitle: campaign.campaignTitle,
      templateName: 'content_approved',
      subject: 'Your content was approved',
      to: application.email
    })
    }

     if(status == "Rejected"){
       sendEmail({
      userName: application.name,
      campaignTitle: campaign.campaignTitle,
      templateName: 'content_rejected',
      subject: 'Please revise your content',
      to: application.email
    })
    }
   


    res.json({
      status: "success",
      message: `Content status updated to ${status}`,
      data: {
        id: application._id,
        contentStatus: status === 'Approved' ? '✅ Submitted' : status === 'Rejected' ? '❌ Rejected' : 'Pending'
      }
    });

  } catch (error) {
    console.error("Error updating content status:", error);
    res.json({
      status: "failed",
      message: "Failed to update content status"
    });
  }
});


module.exports = brandCreatorRouter;
