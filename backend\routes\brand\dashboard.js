// routes/brand/dashboard.js
const express = require('express');
const { getDashboardData } = require('../../middlewares/brand/dashboardController');
const { verifyBrandToken } = require('../../middlewares/brand/authController');

const router = express.Router();

// GET /api/brand/dashboard/data
// Returns usage, remaining campaigns, expiry, upgrade window
router.get('/data', verifyBrandToken, getDashboardData);

module.exports = router;
