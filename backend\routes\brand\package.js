// routes/brand/package.js
const express = require('express');
const router = express.Router();
const { VerifyTokenAuth } = require('../../middlewares/AdminAuth');
// middlewares
const {
  getAllPlans,
  createPlan,
  updatePlan,
  deletePlan
} = require('../../middlewares/brand/planController');
const {
  purchasePlan,
  upgradePlan,
  purchaseAddon,
  getSubscription
} = require('../../middlewares/brand/subscriptionController');
const { getMyPaymentHistory } = require('../../middlewares/brand/paymentController');
const { activateFreeTrial } = require('../../controllers/trialController');
const { verifyBrandToken } = require('../../middlewares/brand/authController');

// Public routes
router.get('/plans', getAllPlans);

// Protected brand routes
router.post('/subscribe', verifyBrandToken, purchasePlan);
router.post('/upgrade', verifyBrandToken, upgradePlan);
router.post('/addon', verifyBrandToken, purchaseAddon);
router.get('/subscription', verifyBrandToken, getSubscription);

router.get('/payment-history', verifyBrandToken, getMyPaymentHistory);

router.post("/activate-trial", verifyBrandToken, activateFreeTrial);

// Admin-only plan management (requires brand to have admin role)
router.post('/plans', VerifyTokenAuth, createPlan);
router.patch('/plans/:id', VerifyTokenAuth, updatePlan);
router.delete('/plans/:id', VerifyTokenAuth, deletePlan);

module.exports = router;
