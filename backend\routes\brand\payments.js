// routes/brand/payment-history.js
const express = require('express');
const router = express.Router();

// Controllers
const { getMyPaymentHistory } = require('../../middlewares/brand/paymentController');
const { verifyBrandToken } = require('../../middlewares/brand/authController');

/**
 * GET /brand/payment-history
 * Protected route: Return all payment history entries for the authenticated brand
 */
router.get('/', verifyBrandToken, getMyPaymentHistory);

module.exports = router;
