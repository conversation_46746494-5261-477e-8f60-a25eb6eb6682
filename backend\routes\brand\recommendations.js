const express = require("express");
const router = express.Router();
const { verifyBrandToken } = require("../../middlewares/brand/authController");
const { Campaign, User } = require("../../database");
const { scoreForCampaign } = require("../../services/scoring");

// GET /api/recommendations?campaignId=...
router.get("/", verifyBrandToken, async (req, res) => {
  try {
    const { campaignId } = req.query;
    if (!campaignId) {
      return res.status(400).json({ status: "error", message: "campaignId is required" });
    }

    // Fetch campaign
    const campaign = await Campaign.findById(campaignId);
    if (!campaign) return res.status(404).json({ status: "error", message: "Campaign not found" });

    // Fetch creators who have submitted URLs
    const creators = await User.find({ urlsSubmitted: true });

    const scored = creators
      .map((creator) => {
        const { score, reasons } = scoreForCampaign(creator, campaign);

        return {
          creatorId: creator._id,
          name: creator.name,
          profilePic: creator.avatar || creator.profilePic || null,
          tiktokId: creator.sns?.tiktok?.handle || creator.tiktokId || null,
          instagramId: creator.sns?.instagram?.handle || creator.instagramId || null,
          platform: creator.sns
            ? Object.keys(creator.sns)
                .filter((k) => creator.sns[k])
                .join(" / ")
            : "-",
          followers: creator.sns
            ? Object.values(creator.sns).reduce((sum, a) => sum + (a?.followerCount || 0), 0)
            : 0,
          engagement: ((creator.contentScore || 0) / 25).toFixed(1), // placeholder
          tags: creator.creatorTags || [],
          description: creator.predictedCategory?.length
            ? `Focus: ${creator.predictedCategory.join(", ")}`
            : "",
          whyRecommended: reasons.slice(0, 4),
          aiScore: score,
        };
      })
      // Filter only creators with score >= 2
      .filter((c) => c.aiScore >= 2);

    // Sort descending by AI score
    scored.sort((a, b) => b.aiScore - a.aiScore);

    res.json({ status: "success", data: scored });
  } catch (e) {
    console.error(e);
    res.status(500).json({ status: "error", message: "Server error" });
  }
});

module.exports = router;
