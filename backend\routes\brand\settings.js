const express = require('express');
const router  = express.Router();
const multer  = require('multer');
const { verifyBrandToken } = require('../../middlewares/brand/authController');
const settingsCtrl          = require('../../middlewares/brand/brandSettingsController');

// store temp uploads here
const upload = multer({ dest: 'uploads/' });

// GET  /api/brand/settings
router.get(
  '/',
  verifyBrandToken,
  settingsCtrl.getBrandSettings
);

// PUT  /api/brand/settings
// expects fields + optional file "logo"
router.put(
  '/',
  verifyBrandToken,
  upload.single('logo'),
  settingsCtrl.updateBrandSettings
);

module.exports = router;
