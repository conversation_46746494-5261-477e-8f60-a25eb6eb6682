const express = require("express");
const { verifyBrandToken } = require("../../middlewares/brand/authController");
const { Types } = require('mongoose');
const {
  Campaign,
  campaignSubmission,
  appliedCampaigns,
  ExtensionRequest,
} = require('../../database/index');

const brandTodoRouter = express.Router();

// Priority mapping for sorting
const PRIORITY = { tracking: 4, content: 3, extension: 2, application: 1, recruitment: 0 };

// Task description generator
const description = (type, extra = {}) => {
  switch (type) {
    case 'tracking':
      return `Enter tracking number for ${extra.creatorName || extra.email}`;
    case 'content':
      return `Review submitted content from ${extra.creatorName || extra.email}`;
    case 'extension':
      return `Review extension request (+${extra.daysRequested} days) from ${extra.creatorName}`;
    case 'application':
      return `Review application from ${extra.creatorName}`;
    default:
      return 'Campaign has 0 applications – needs promotion';
  }
};

// Normalize task data
const normalise = (doc) => ({
  id: `${doc.type}-${doc._id.toString()}`,
  type: doc.type,
  campaignId: doc.campaignId?.toString(),
  campaignTitle: doc.campaignTitle,
  creatorId: doc.creatorId || null,
  creatorName: doc.creatorName || null,
  description: description(doc.type, doc),
  priority: PRIORITY[doc.type] ?? 0,
  createdAt: doc.createdAt || new Date(),
  dueDate: doc.dueDate || null,
  metadata: doc.metadata || {},
});

// GET /api/brand/todos - Get actionable tasks for the brand dashboard
brandTodoRouter.get("/", verifyBrandToken, async (req, res) => {
  try {
    const brandId = req.user.id;
    const brandFilter = { brand: Types.ObjectId(brandId) };

    // Fetch tracking tasks
    const tracking = await campaignSubmission.aggregate([
      { $match: {
        $or: [
          { 'tracking_info.tracking_number': '' },
          { 'tracking_info.tracking_number': { $exists: false } },
        ],
      }},
      { $lookup: {
        from: 'campaigns', localField: 'campaign_id', foreignField: '_id', as: 'campaign',
      }},
      { $unwind: '$campaign' },
      { $match: brandFilter },
      { $project: {
        _id: '$_id',
        type: { $literal: 'tracking' },
        campaignId: '$campaign._id',
        campaignTitle: '$campaign.campaignTitle',
        creatorId: '$user_id',
        creatorName: '$email',
        dueDate: '$campaign.deadline',
      }},
    ]);

    // Fetch content review tasks
    const content = await campaignSubmission.aggregate([
      { $match: { content_status: 'Pending' }},
      { $lookup: {
        from: 'campaigns', localField: 'campaign_id', foreignField: '_id', as: 'campaign',
      }},
      { $unwind: '$campaign' },
      { $match: brandFilter },
      { $project: {
        _id: '$_id',
        type: { $literal: 'content' },
        campaignId: '$campaign._id',
        campaignTitle: '$campaign.campaignTitle',
        creatorId: '$user_id',
        creatorName: '$email',
        dueDate: '$campaign.deadline',
      }},
    ]);

    // Fetch extension request tasks
    const extension = await ExtensionRequest.aggregate([
      { $match: { status: 'pending' }},
      { $lookup: {
        from: 'campaigns', localField: 'campaign', foreignField: '_id', as: 'campaign',
      }},
      { $unwind: '$campaign' },
      { $match: brandFilter },
      { $project: {
        _id: '$_id',
        type: { $literal: 'extension' },
        campaignId: '$campaign._id',
        campaignTitle: '$campaign.campaignTitle',
        creatorId: '$creator',
        creatorName: '$creatorName',
        daysRequested: '$daysRequested',
        dueDate: '$campaign.deadline',
      }},
    ]);

    // Fetch application review tasks
    const application = await appliedCampaigns.aggregate([
      { $match: { status: 'Pending' }},
      { $lookup: {
        from: 'campaigns', localField: 'campaign', foreignField: '_id', as: 'campaign',
      }},
      { $unwind: '$campaign' },
      { $match: brandFilter },
      { $project: {
        _id: '$_id',
        type: { $literal: 'application' },
        campaignId: '$campaign._id',
        campaignTitle: '$campaign.campaignTitle',
        creatorId: '$creator',
        creatorName: '$name',
        dueDate: '$campaign.recruitmentEndDate',
      }},
    ]);

    // Fetch recruitment tasks
    const recruitment = await Campaign.aggregate([
      { $match: { ...brandFilter, status: { $in: ['Active', 'Pending'] }}},
      { $lookup: {
        from: 'appliedcampaigns', localField: '_id', foreignField: 'campaign', as: 'apps',
      }},
      { $addFields: { applicationCount: { $size: '$apps' }}},
      { $match: { applicationCount: 0 }},
      { $project: {
        _id: '$_id',
        type: { $literal: 'recruitment' },
        campaignId: '$_id',
        campaignTitle: '$campaignTitle',
        dueDate: '$recruitmentEndDate',
      }},
    ]);

    // Combine and normalize all tasks
    let tasks = [
      ...tracking,
      ...content,
      ...extension,
      ...application,
      ...recruitment,
    ].map(normalise);

    // Sort by priority and due date
    tasks.sort((a, b) =>
      PRIORITY[b.type] - PRIORITY[a.type] ||
      new Date(a.dueDate || Infinity) - new Date(b.dueDate || Infinity)
    );

    // Take only top 4 priority items for the todo widget
    const topTodos = tasks.slice(0, 4);

    // Transform to todo format
    const todos = topTodos.map(task => ({
      id: task.id,
      type: task.type,
      title: task.description,
      description: `Campaign: ${task.campaignTitle}`,
      priority: task.priority >= 3 ? 'high' : task.priority >= 1 ? 'medium' : 'low',
      count: 1,
      action: getTaskAction(task.type),
      link: getTaskLink(task),
      campaignId: task.campaignId,
      creatorName: task.creatorName,
      dueDate: task.dueDate
    }));

    res.json({
      status: "success",
      todos: todos
    });
  } catch (error) {
    console.error("Error fetching brand todos:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch todos"
    });
  }
});

// GET /api/brand/todos/summary - Get recent tasks summary for the brand dashboard
brandTodoRouter.get("/summary", verifyBrandToken, async (req, res) => {
  try {
    const brandId = req.user.id;
    const brandFilter = { brand: Types.ObjectId(brandId) };

    // Fetch tracking tasks
    const tracking = await campaignSubmission.aggregate([
      { $match: {
        $or: [
          { 'tracking_info.tracking_number': '' },
          { 'tracking_info.tracking_number': { $exists: false } },
        ],
      }},
      { $lookup: {
        from: 'campaigns', localField: 'campaign_id', foreignField: '_id', as: 'campaign',
      }},
      { $unwind: '$campaign' },
      { $match: brandFilter },
      { $project: {
        _id: '$_id',
        type: { $literal: 'tracking' },
        campaignId: '$campaign._id',
        campaignTitle: '$campaign.campaignTitle',
        creatorId: '$user_id',
        creatorName: '$email',
        dueDate: '$campaign.deadline',
      }},
    ]);

    // Fetch content review tasks
    const content = await campaignSubmission.aggregate([
      { $match: { content_status: 'Pending' }},
      { $lookup: {
        from: 'campaigns', localField: 'campaign_id', foreignField: '_id', as: 'campaign',
      }},
      { $unwind: '$campaign' },
      { $match: brandFilter },
      { $project: {
        _id: '$_id',
        type: { $literal: 'content' },
        campaignId: '$campaign._id',
        campaignTitle: '$campaign.campaignTitle',
        creatorId: '$user_id',
        creatorName: '$email',
        dueDate: '$campaign.deadline',
      }},
    ]);

    // Fetch extension request tasks
    const extension = await ExtensionRequest.aggregate([
      { $match: { status: 'pending' }},
      { $lookup: {
        from: 'campaigns', localField: 'campaign', foreignField: '_id', as: 'campaign',
      }},
      { $unwind: '$campaign' },
      { $match: brandFilter },
      { $project: {
        _id: '$_id',
        type: { $literal: 'extension' },
        campaignId: '$campaign._id',
        campaignTitle: '$campaign.campaignTitle',
        creatorId: '$creator',
        creatorName: '$creatorName',
        daysRequested: '$daysRequested',
        dueDate: '$campaign.deadline',
      }},
    ]);

    // Fetch application review tasks
    const application = await appliedCampaigns.aggregate([
      { $match: { status: 'Pending' }},
      { $lookup: {
        from: 'campaigns', localField: 'campaign', foreignField: '_id', as: 'campaign',
      }},
      { $unwind: '$campaign' },
      { $match: brandFilter },
      { $project: {
        _id: '$_id',
        type: { $literal: 'application' },
        campaignId: '$campaign._id',
        campaignTitle: '$campaign.campaignTitle',
        creatorId: '$creator',
        creatorName: '$name',
        dueDate: '$campaign.recruitmentEndDate',
      }},
    ]);

    // Fetch recruitment tasks
    const recruitment = await Campaign.aggregate([
      { $match: { ...brandFilter, status: { $in: ['Active', 'Pending'] }}},
      { $lookup: {
        from: 'appliedcampaigns', localField: '_id', foreignField: 'campaign', as: 'apps',
      }},
      { $addFields: { applicationCount: { $size: '$apps' }}},
      { $match: { applicationCount: 0 }},
      { $project: {
        _id: '$_id',
        type: { $literal: 'recruitment' },
        campaignId: '$_id',
        campaignTitle: '$campaignTitle',
        dueDate: '$recruitmentEndDate',
      }},
    ]);

    // Combine and normalize all tasks
    let tasks = [
      ...tracking,
      ...content,
      ...extension,
      ...application,
      ...recruitment,
    ].map(normalise);

    // Sort by priority and due date
    tasks.sort((a, b) =>
      PRIORITY[b.type] - PRIORITY[a.type] ||
      new Date(a.dueDate || Infinity) - new Date(b.dueDate || Infinity)
    );

    // Take only top 4 recent tasks for the summary
    const recentTasks = tasks.slice(0, 4);

    res.json({
      status: "success",
      recentTasks: recentTasks
    });
  } catch (error) {
    console.error("Error fetching brand todos summary:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch todos summary"
    });
  }
});

// Helper function to get task action
const getTaskAction = (type) => {
  switch (type) {
    case 'tracking': return 'Input Tracking';
    case 'content': return 'Review Content';
    case 'extension': return 'Review Request';
    case 'application': return 'Review Creators';
    case 'recruitment': return 'View Campaign';
    default: return 'View Details';
  }
};

// Helper function to get task link
const getTaskLink = (task) => {
  const { type, campaignId } = task;
  switch (type) {
    case 'tracking':
      return `/brand/campaigns/${campaignId}?tab=creators&filter=trackingPending`;
    case 'content':
      return `/brand/campaigns/${campaignId}/content`;
    case 'extension':
      return `/brand/campaigns/${campaignId}?tab=creators&filter=extensionPending`;
    case 'application':
      return `/brand/campaigns/${campaignId}?tab=creators&filter=applied`;
    case 'recruitment':
      return `/brand/campaigns/${campaignId}?tab=creators`;
    default:
      return `/brand/campaigns/${campaignId}`;
  }
};

module.exports = brandTodoRouter;
