// routes/webhooks.js
const express = require('express');
const router = express.Router();

// Controller for Stripe webhook events
// Adjusted path to point to the middlewares directory
const { handleStripeWebhook } = require('../../middlewares/brand/webhookController');

/**
 * POST /webhooks/stripe
 * Stripe webhook endpoint (must use raw body parser for signature verification)
 */
router.post(
  '/stripe',
  express.raw({ type: 'application/json' }),
  handleStripeWebhook
);

module.exports = router;
