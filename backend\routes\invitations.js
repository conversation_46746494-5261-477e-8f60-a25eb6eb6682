
const { verifyBrandToken } = require("../middlewares/brand/authController");

const { VerifyToken } = require("../middlewares/auth");
const express = require("express");
const { User, Campaign, Invitation } = require("../database");

const router = express.Router();

/**
 * Helper for uniform responses
 */
const sendRes = (res, status, message, data = null, code = 200) => {
  res.status(code).json({ status, message, data });
};

/**
 * @route   GET /api/invitations/bell
 * @desc    For 🔔 bell dropdown: last 5 invites (pending only)
 * @access  Creator only
 */
router.get("/bell", VerifyToken, async (req, res) => {
  try {
    const invites = await Invitation.find({
      creator: req.user.id,
      status: "pending"
    })
      .populate({
        path: "campaign",
        select: "name thumbnail brand",
        populate: { path: "brand", select: "name logo" }
      })
      .sort({ createdAt: -1 })
      .limit(5);

    return sendRes(res, "success", "Fetched recent pending invites", invites);
  } catch (err) {
    console.error("Bell error:", err);
    return sendRes(res, "error", "Server error", null, 500);
  }
});

/**
 * @route   GET /api/invitations
 * @desc    Get logged-in creator’s invitations (latest first)
 * @access  Creator only
 */
router.get("/", VerifyToken, async (req, res) => {
  try {
    const invites = await Invitation.find({ creator: req.user.id })
      .populate({
        path: "campaign",
        select: "name thumbnail brand deadline reward type",
        populate: { path: "brand", select: "name logo" }
      })
      .sort({ createdAt: -1 });

    return sendRes(res, "success", "Fetched all invites", invites);
  } catch (err) {
    console.error("Error fetching invites:", err);
    return sendRes(res, "error", "Server error", null, 500);
  }
});

/**
 * @route   POST /api/invitations
 * @desc    Brand invites a creator to campaign
 * @access  Brand only
 */
router.post("/", verifyBrandToken, async (req, res) => {
  try {
    const { campaignId, creatorId } = req.body;

    const campaign = await Campaign.findById(campaignId);
    const creator = await User.findById(creatorId);

    if (!campaign || !creator) {
      return sendRes(res, "error", "Campaign or Creator not found", null, 404);
    }

    // prevent duplicate
    const existing = await Invitation.findOne({ campaign: campaignId, creator: creatorId });
    if (existing) {
      return sendRes(res, "error", "Creator already invited", null, 400);
    }

    const invite = new Invitation({
      campaign: campaignId,
      creator: creatorId,
      status: "pending",
      expiresAt: campaign.deadline
    });

    await invite.save();
    return sendRes(res, "success", "Invitation created", invite, 201);
  } catch (err) {
    console.error("Error creating invite:", err);
    return sendRes(res, "error", "Server error", null, 500);
  }
});

/**
 * @route   PATCH /api/invitations/:id/accept
 * @desc    Creator accepts invite
 * @access  Creator only
 */
router.patch("/:id/accept", VerifyToken, async (req, res) => {
  try {
    const invite = await Invitation.findOne({
      _id: req.params.id,
      creator: req.user.id
    });

    if (!invite) return sendRes(res, "error", "Invitation not found", null, 404);

    // check expiry
    if (invite.expiresAt && invite.expiresAt < new Date()) {
      invite.status = "expired";
      await invite.save();
      return sendRes(res, "error", "Invitation expired", invite, 410);
    }

    if (invite.status !== "pending") {
      return sendRes(res, "error", "Invitation already handled", invite, 403);
    }

    invite.status = "accepted";
    await invite.save();

    return sendRes(res, "success", "Invitation accepted", invite);
  } catch (err) {
    console.error("Accept error:", err);
    return sendRes(res, "error", "Server error", null, 500);
  }
});

/**
 * @route   PATCH /api/invitations/:id/decline
 * @desc    Creator declines invite
 * @access  Creator only
 */
router.patch("/:id/decline", VerifyToken, async (req, res) => {
  try {
    const invite = await Invitation.findOne({
      _id: req.params.id,
      creator: req.user.id
    });

    if (!invite) return sendRes(res, "error", "Invitation not found", null, 404);

    if (invite.status !== "pending") {
      return sendRes(res, "error", "Invitation already handled", invite, 403);
    }

    invite.status = "declined";
    await invite.save();

    return sendRes(res, "success", "Invitation declined", invite);
  } catch (err) {
    console.error("Decline error:", err);
    return sendRes(res, "error", "Server error", null, 500);
  }
});

module.exports = router;
