
const { VerifyToken } = require("../middlewares/auth");
// routes/socialAuth.js
const express = require("express");
const axios = require("axios");
const {User} = require("../database");

const router = express.Router();

/**
 * @route GET /user-social/accounts
 * @desc Get all connected social accounts for the logged-in user
 */
router.get("/accounts", VerifyToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select("sns snsConnected");
    res.json({ status: "success", accounts: user.sns });
  } catch (err) {
    res.status(500).json({ status: "error", message: "Failed to fetch accounts" });
  }
});

/**
 * @route DELETE /user-social/disconnect/:provider
 * @desc Disconnect a social account (Instagram/TikTok)
 */
router.delete("/disconnect/:provider", VerifyToken, async (req, res) => {
  try {
    const { provider } = req.params;
    if (!["instagram", "tiktok"].includes(provider)) {
      return res.status(400).json({ status: "error", message: "Invalid provider" });
    }

    const user = await User.findById(req.user.id);
    user.sns[provider] = undefined; // remove data
    user.snsConnected = Object.values(user.sns).some((acc) => acc); // true if any connected
    await user.save();

    res.json({ status: "success", message: `${provider} disconnected` });
  } catch (err) {
    res.status(500).json({ status: "error", message: "Failed to disconnect account" });
  }
});

/**
 * @route GET /user-social/stats/:provider
 * @desc Fetch saved stats from DB for a specific provider
 */
router.get("/stats/:provider", VerifyToken, async (req, res) => {
  try {
    const { provider } = req.params;
    const user = await User.findById(req.user.id).select(`sns.${provider}`);
    if (!user.sns[provider]) {
      return res.status(404).json({ status: "error", message: "Account not connected" });
    }
    res.json({ status: "success", stats: user.sns[provider] });
  } catch (err) {
    res.status(500).json({ status: "error", message: "Failed to fetch stats" });
  }
});

module.exports = router;