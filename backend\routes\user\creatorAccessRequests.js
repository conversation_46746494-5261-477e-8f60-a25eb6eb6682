const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");
const { User, appliedCampaigns, CreatorAccessRequest, campaignSubmission } = require("../../database");

const creatorAccessRouter = express.Router();

// POST /api/creator-access-requests
// Creates a new access request for the current logged-in creator
creatorAccessRouter.post("/", VerifyToken, async (req, res) => {
  try {
    const { email } = req.user;

    // Find the creator
    const creator = await User.findOne({ email });
    if (!creator) {
      return res.status(404).json({
        status: "error",
        message: "Creator not found",
      });
    }

    // Check if creator already has a pending or approved request
    const existingRequest = await CreatorAccessRequest.findOne({
      creator: creator._id,
      status: { $in: ["pending", "approved"] },
    }); 

    if (existingRequest) {
      return res.status(400).json({
        status: "error",
        message: existingRequest.status === "pending" 
          ? "You already have a pending request" 
          : "You already have access to paid campaigns",
      });
    }

    // Check if creator has completed at least one Gifted Campaign
    const giftedCampaigns = await appliedCampaigns.find({
      email,
      status: { $in: ["Submitted", "Approved"] },
    }).populate("campaign");

    const completedGiftedCampaigns = giftedCampaigns.filter(app => 
      app.campaign && app.campaign.campaignType === "gifted"
    );

    
    if (completedGiftedCampaigns.length === 0) {
      return res.status(400).json({
        status: "error",
        message: "You must complete at least one gifted campaign before requesting access to paid campaigns",
      });
    }



    // Get the most recent gifted campaign application for social handle info
    const mostRecentGifted = completedGiftedCampaigns
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];

    // Determine platform and handle
    let platform = "Instagram";
    let socialHandle = mostRecentGifted.instagramId || "";
    let socialLink = "";

    if (mostRecentGifted.tiktokId && mostRecentGifted.tiktokId.trim()) {
      platform = "TikTok";
      socialHandle = mostRecentGifted.tiktokId.replace("@", "");
      socialLink = `https://www.tiktok.com/@${socialHandle}`;
    } else if (mostRecentGifted.instagramId && mostRecentGifted.instagramId.trim()) {
      socialHandle = mostRecentGifted.instagramId.replace("@", "");
      socialLink = `https://www.instagram.com/${socialHandle}`;
    } else {
      return res.status(400).json({
        status: "error",
        message: "No social media handle found in your recent gifted campaign applications",
      });
    }

         const submission = await campaignSubmission.findOne({ campaign_id: "682f58586fda45dba090da7c", email });
        if (!submission) {
          return res.status(404).json({ status: "failed", message: "Please submit Url for Paid Campaign" });
        }


    // Create the access request
    const accessRequest = new CreatorAccessRequest({
      creator: creator._id,
      platform,
      socialHandle,
      socialLink,
      giftedCampaignsSubmitted: completedGiftedCampaigns.length,
    });

    await accessRequest.save();

    // // Update creator's paid_status
    // creator.paid_status = "pending_admin_approval";
    // await creator.save();

    res.status(201).json({
      status: "success",
      message: "Access request submitted successfully",
      data: {
        id: accessRequest._id,
        status: accessRequest.status,
        platform: accessRequest.platform,
        socialHandle: accessRequest.socialHandle,
        giftedCampaignsSubmitted: accessRequest.giftedCampaignsSubmitted,
        createdAt: accessRequest.createdAt,
      },
    });
  } catch (error) {
    console.error("Error creating access request:", error);
    res.status(500).json({
      status: "error",
      message: "Internal server error",
    });
  }
});

// GET /api/creator-access-requests/status
// Check the status of the current creator's access request
creatorAccessRouter.get("/status", VerifyToken, async (req, res) => {
  try {
    const { email } = req.user;
    console.log(email);

    const creator = await User.findOne({ email });
    if (!creator) {
      return res.status(404).json({
        status: "error",
        message: "Creator not found",
      });
    }

      // Check if creator has completed at least one Gifted Campaign
    const giftedCampaigns = await appliedCampaigns.find({
      email,
      status: { $in: ["Submitted", "Approved"] },
    }).populate("campaign");

    const completedGiftedCampaigns = giftedCampaigns.filter(app => 
      app.campaign && app.campaign.campaignType === "gifted"
    );
    
    if (completedGiftedCampaigns.length === 0) {
      return res.status(400).json({
        status: "error",
        message: "You must complete at least one gifted campaign before requesting access to paid campaigns",
      });
    }

    // Get the most recent gifted campaign application for social handle info
    const mostRecentGifted = completedGiftedCampaigns
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];

    // Determine platform and handle
    let platform = "Instagram";
    let socialHandle = mostRecentGifted.instagramId || "";
    let socialLink = "";

    if (mostRecentGifted.tiktokId && mostRecentGifted.tiktokId.trim()) {
      platform = "TikTok";
      socialHandle = mostRecentGifted.tiktokId.replace("@", "");
      socialLink = `https://www.tiktok.com/@${socialHandle}`;
    } else if (mostRecentGifted.instagramId && mostRecentGifted.instagramId.trim()) {
      socialHandle = mostRecentGifted.instagramId.replace("@", "");
      socialLink = `https://www.instagram.com/${socialHandle}`;
    } else {
      return res.status(400).json({
        status: "error",
        message: "No social media handle found in your recent gifted campaign applications",
      });
    }


    const accessRequest = await CreatorAccessRequest.findOne({
      creator: creator._id,
    }).sort({ createdAt: -1 });

    // if (!accessRequest) {
    //   return res.status(404).json({
    //     status: "error",
    //     message: "Access Request not found",
    //   });
    // }
 

    res.status(200).json({
      status: "success",
      data: {
        paid_status: creator.paid_status,
        accessRequest: accessRequest ? {
          id: accessRequest._id,
          status: accessRequest.status,
          platform: accessRequest.platform,
          socialHandle: accessRequest.socialHandle,
          giftedCampaignsSubmitted: accessRequest.giftedCampaignsSubmitted,
          followerCount: accessRequest.followerCount,
          rejectionReason: accessRequest.rejectionReason,
          createdAt: accessRequest.createdAt,
          approvedAt: accessRequest.approvedAt,
          rejectedAt: accessRequest.rejectedAt,
        } : null,
      },
    });
  } catch (error) {
    console.error("Error fetching access request status:", error);
    res.status(500).json({
      status: "error",
      message: "Internal server error",
    });
  }
});

module.exports = creatorAccessRouter; 