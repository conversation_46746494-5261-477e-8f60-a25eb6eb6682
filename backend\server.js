/** @format */

const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const helmet = require('helmet'); // ✅ added helmet
const rateLimit = require('express-rate-limit'); // ✅ global rate limiter
const cloudinary = require('cloudinary').v2;
const multer = require('multer');
const mongoose = require('mongoose');
const path = require('path');

dotenv.config();

// Existing routes
const { Auth } = require('./routes/Auth');
const { AdminAuth } = require('./routes/Admin/Auth');
const { Campaigns } = require('./routes/Admin/campaigns');
const { application } = require('./routes/Admin/Applications');
const { registered } = require('./routes/Admin/registered');
const { adminRouter } = require('./routes/Admin/rewardsAdmin');
const { user } = require('./routes/user/user');
const { CampaignsPublic } = require('./middlewares/public');
const campaignRoutes = require('./routes/user/campaignsubmission');
const adminBrandApproveRoutes = require('./routes/Admin/brands');
const adminBrandPlanRoutes = require('./routes/Admin/brandPlanManagement');
const adminPaymentRoutes = require('./routes/Admin/payments');
const brandTaskRoutes = require('./routes/brand/tasks');

// ✅ NEW: Creator access request routes
const creatorAccessRoutes = require('./routes/user/creatorAccessRequests');
const adminCreatorAccessRoutes = require('./routes/Admin/creatorAccessRequests');

// Brand payment routes
const brandPaymentRoutes = require('./routes/brand/payments');

// New brand routes
const brandAuthRouter = require('./routes/brand/auth');
const brandPackageRouter = require('./routes/brand/package');
const brandDashboardRouter = require('./routes/brand/dashboard');
const brandTrackingRouter = require('./routes/brand/tracking');
const brandApplicationRoutes = require('./routes/brand/applications');
const brandCampaignRequestRoutes = require('./routes/brand/campaignRequest');
const adminCampaignApprovalRoutes = require('./routes/Admin/campaignRequest');
const brandSettingsRouter = require('./routes/brand/settings');
const brandCampaignApplyRoutes = require('./routes/brand/brandCampaignApplyRoutes');
const brandSubmissionRoutes = require("./routes/brand/brandSubmissionRoutes");
const brandCampaignRouter = require('./routes/brand/campaigns');
const brandTodoRouter = require('./routes/brand/todos');
const brandCreatorRouter = require('./routes/brand/creators');
const brandPerformanceRouter = require('./routes/brand/performance');
const brandTaskRouter = require('./routes/brand/tasks');
const brandRouter = require("./routes/brand/index")
const { brandUsers } = require("./routes/brand/brandUsers");
const creatorRouter = require('./routes/creator');

const trackWebhook = require('./webhooks/trackWebhook');


const SubmissionDelays= require('./routes/Admin/SubmissionDelays');

// ✅ NEW
const socialAuthRoutes = require('./routes/authSocial');
const userSocialRoutes = require('./routes/userSocial');
const invitationRoutes = require('./routes/invitations');


const recommendationsRouter = require("./routes/brand/recommendations");



// Webhook route
const webhooks = require('./routes/brand/webhooks');

// ✅ NEW: AI Generate routes
const aiGenerateRoutes = require('./routes/ai/aiGenerate');
const sendEmail = require('./utils/sendEmail');

// File upload setup
const upload = multer({ dest: 'uploads/' });

// ✅ CORS Origins for Railway deployment
const allowedOrigins = [
   'http://localhost:3000', // ✅ Local development frontend
  'http://localhost:3001', // ✅ Local development frontend
  'https://matchably.kr',              // ✅ Production frontend
  'https://www.matchably.kr',          // ✅ Production frontend
  'https://guide-frontend.vercel.app', // ✅ Vercel dev deployment
  'guide-frontend-git-vikas-vikas-projects-53f33011.vercel.app', // ✅ Vercel production deployment
];

const PORT = process.env.PORT || 5000; // ✅ Added fallback port

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

const app = express();

//require('./jobs/contentReminderCron')
//require('./jobs/updatePerformance')

const { startInviteReminderJob } = require("./jobs/inviteReminders");

//startInviteReminderJob();

// ✅ FIXED: Configure Helmet to allow your frontend
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" },
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// ✅ Health check route (before rate limiting and CORS)
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    port: PORT,
    environment: process.env.NODE_ENV || 'development'
  });
});

// ✅ Global rate limiter (200 requests/min per IP) - AFTER health check
const globalLimiter = rateLimit({
  windowMs: 60 * 1000,
  max: 200,
  message: { status: 'failed', message: 'Too many requests. Try again in 1 minute.' },
  skip: (req) => req.path === '/health' // Skip rate limiting for health check
});
app.use(globalLimiter);

// ✅ FIXED: Enhanced CORS setup with better error handling
app.use(
  cors({
    origin: function (origin, callback) {
      // Allow requests with no origin (mobile apps, curl, Postman, etc.)
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        console.log('❌ CORS blocked origin:', origin);
        callback(new Error(`Not allowed by CORS. Origin: ${origin}`));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'Cache-Control',
      'X-Access-Token'
    ],
  })
);

// ✅ Trust proxy to use correct client IP (required behind proxies)
//app.set('trust proxy', true);

// ✅ Handle preflight requests explicitly
app.options('*', cors());

// ✅ Stripe webhook must be mounted before JSON parsing
app.use('/webhooks', webhooks);
app.use('/api/webhook', trackWebhook);

// ✅ JSON body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// ✅ FIXED: Serve static files with proper MIME types
app.use(express.static(path.join(__dirname, 'dist'), {
  setHeaders: (res, filePath) => {
    if (filePath.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css');
    } else if (filePath.endsWith('.js')) {
      res.setHeader('Content-Type', 'application/javascript');
    } else if (filePath.endsWith('.html')) {
      res.setHeader('Content-Type', 'text/html');
    }
  }
}));

// ✅ Root test route
app.get('/', (req, res) => {
  res.json({ app: 'app is live', timestamp: new Date().toISOString() });
});

// ✅ Existing API routes
app.use('/api/admin/submission-delays',SubmissionDelays)
app.use('/api/admin/car', adminCreatorAccessRoutes);
app.use('/api/brand/tasks', brandTaskRoutes);
app.use('/api/auth', Auth);
app.use('/api/admin', AdminAuth);
app.use('/api/admin/campaigns', Campaigns);
app.use('/api/admin/applications', application);
app.use('/api/admin/users', registered);
app.use('/api/admin/referrel', adminRouter);
app.use('/api/admin/campaign-approvals', adminCampaignApprovalRoutes);
app.use('/api/user/campaigns', CampaignsPublic);
app.use('/api/user/campaigns', user);
app.use('/api/user', campaignRoutes);
app.use('/api/admin/brands', adminBrandApproveRoutes);
app.use('/api/admin/brands', adminBrandPlanRoutes);



app.use("/api/user-social", userSocialRoutes);


app.use("/api/social", socialAuthRoutes);
app.use("/api/invitations", invitationRoutes);


app.use("/api/recommendations", recommendationsRouter);

// ✅ NEW: Creator access request routes
app.use('/api/car', creatorAccessRoutes);
app.use('/api/creator', creatorRouter);

// ✅ Admin payment routes
app.use('/api/admin/payments', adminPaymentRoutes);

// ✅ NEW: AI Generate routes
app.use('/api/ai', aiGenerateRoutes);

app.use('/api/brand/auth', brandAuthRouter);
app.use('/api/brand/package', brandPackageRouter);
app.use('/api/brand/dashboard', brandDashboardRouter);
app.use('/api/brand/tracking', brandTrackingRouter);
app.use('/api/brand/payments', brandPaymentRoutes);
app.use('/api/brand/campaign-request', brandCampaignRequestRoutes);
app.use('/api/brand/applications', brandApplicationRoutes);
app.use('/api/brand/settings', brandSettingsRouter);
app.use("/api/brand/users", brandUsers);

// ✅ Mount your specific brand routes directly
app.use('/api/brand/campaigns', brandCampaignRouter);
app.use('/api/brand/todos', brandTodoRouter);
app.use('/api/brand/creators', brandCreatorRouter);
app.use('/api/brand/performance', brandPerformanceRouter);
app.use('/api/brand/tasks', brandTaskRouter);
app.use('/api', brandSubmissionRoutes);



// ✅ Mount brandCampaignApplyRoutes (has routes like /campaign-apply/*)
app.use('/api/brand', brandCampaignApplyRoutes);

// ✅ File upload route with better error handling
app.post('/api/upload', upload.single('image'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ 
      status: 'failed',
      message: 'No file uploaded' 
    });
  }

  try {
    const result = await cloudinary.uploader.upload(req.file.path, {
      folder: 'uploads',
      resource_type: 'auto'
    });
    
    res.status(200).json({ 
      status: 'success',
      imageUrl: result.secure_url,
      publicId: result.public_id
    });
  } catch (error) {
    console.error('❌ Cloudinary upload error:', error);
    res.status(500).json({ 
      status: 'failed',
      message: 'Error uploading to Cloudinary',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// ✅ API 404 handler (must come before catch-all)
app.use('/api/*', (req, res) => {
  res.status(404).json({ 
    status: 'failed',
    message: `API route not found: ${req.method} ${req.path}` 
  });
});

// ✅ Catch-all: serve index.html for any non-API route (for React Router)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'), (err) => {
    if (err) {
      console.error('❌ Error serving index.html:', err);
      res.status(500).json({ message: 'Error serving application' });
    }
  });
});

// ✅ Global error handler
app.use((error, req, res, next) => {
  console.error('❌ Global error:', error);
  
  if (error.message && error.message.includes('CORS')) {
    return res.status(403).json({
      status: 'failed',
      message: 'CORS policy violation',
      origin: req.get('Origin')
    });
  }
  
  res.status(500).json({
    status: 'failed',
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// ✅ MongoDB connection & start server
console.log('🔗 Attempting to connect to MongoDB...');
mongoose
  .connect(process.env.MONGO_URL, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    serverSelectionTimeoutMS: 10000, // 10 seconds
    socketTimeoutMS: 45000, // 45 seconds
    maxPoolSize: 10,
    retryWrites: true,
  })
  .then(() => {
    console.log('✅ Connected to MongoDB');

    // Wait for connection to be ready before accessing database info
    mongoose.connection.once('open', () => {
      console.log('📊 Database:', mongoose.connection.db?.databaseName || 'Connected');
    });

    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log('🚀 Server running on port', PORT);
      console.log('🌐 Host: 0.0.0.0 (Railway requirement)');
      console.log('🌐 Allowed origins:', allowedOrigins);
      console.log('📁 Serving static files from:', path.join(__dirname, 'dist'));
      console.log('🏥 Health check available at: /health');
      console.log('✅ Server is ready to accept connections');
    });

    server.on('error', (error) => {
      console.error('❌ Server error:', error);
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
      }
      process.exit(1);
    });
  })
  .catch((err) => {
    console.error('❌ MongoDB connection failed:', err);
    console.error('❌ MONGO_URL:', process.env.MONGO_URL ? 'Set' : 'Not set');
    process.exit(1);
  });

// ✅ Graceful shutdown
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received, shutting down gracefully');
  mongoose.connection.close(() => {
    console.log('📦 MongoDB disconnected');
    process.exit(0);
  });
});
