const puppeteer = require('puppeteer');
const OpenAI = require('openai');

class AIGenerateService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async scrapeProductPage(url) {
    let browser;
    try {
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage();
      
      // Set user agent to avoid detection
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

      // Extract product information
      const productData = await page.evaluate(() => {
        const data = {
          title: '',
          description: '',
          ingredients: '',
          usage: '',
          category: '',
          brandName: '',
          images: []
        };

        // Try to extract title
        const titleSelectors = [
          'h1',
          '[data-testid="product-title"]',
          '.product-title',
          '.product-name',
          'h1.product-title',
          'h1.product-name'
        ];

        for (const selector of titleSelectors) {
          const element = document.querySelector(selector);
          if (element && element.textContent.trim()) {
            data.title = element.textContent.trim();
            break;
          }
        }

        // Try to extract description
        const descSelectors = [
          '.product-description',
          '.product-details',
          '[data-testid="product-description"]',
          '.description',
          'p[class*="description"]'
        ];

        for (const selector of descSelectors) {
          const element = document.querySelector(selector);
          if (element && element.textContent.trim()) {
            data.description = element.textContent.trim();
            break;
          }
        }

        // Try to extract ingredients
        const ingredientSelectors = [
          '.ingredients',
          '.product-ingredients',
          '[data-testid="ingredients"]',
          'div[class*="ingredient"]'
        ];

        for (const selector of ingredientSelectors) {
          const element = document.querySelector(selector);
          if (element && element.textContent.trim()) {
            data.ingredients = element.textContent.trim();
            break;
          }
        }

        // Try to extract usage instructions
        const usageSelectors = [
          '.usage',
          '.how-to-use',
          '.instructions',
          '[data-testid="usage"]'
        ];

        for (const selector of usageSelectors) {
          const element = document.querySelector(selector);
          if (element && element.textContent.trim()) {
            data.usage = element.textContent.trim();
            break;
          }
        }

        // Try to extract brand name
        const brandSelectors = [
          '.brand-name',
          '.product-brand',
          '[data-testid="brand"]',
          'span[class*="brand"]'
        ];

        for (const selector of brandSelectors) {
          const element = document.querySelector(selector);
          if (element && element.textContent.trim()) {
            data.brandName = element.textContent.trim();
            break;
          }
        }

        // Extract images
        const imageElements = document.querySelectorAll('img[src*="product"], img[alt*="product"], .product-image img');
        data.images = Array.from(imageElements).map(img => img.src).slice(0, 5);

        return data;
      });

      return productData;

    } catch (error) {
      console.error('Error scraping product page:', error);
      throw new Error('Failed to scrape product page');
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  async generateCampaignContent(productData) {
    try {
      const prompt = `
        Based on the following product information, generate a comprehensive campaign brief for a UGC influencer campaign.
        
        Product Information:
        - Title: ${productData.title}
        - Description: ${productData.description}
        - Ingredients: ${productData.ingredients}
        - Usage: ${productData.usage}
        - Brand: ${productData.brandName}
        
        Please generate the following sections:
        
        1. Campaign Tone & Message Guide:
        - Create an engaging tone that matches the product
        - Provide key messaging points for influencers
        
        2. Required Hashtags:
        - Generate 5-8 relevant hashtags
        - Include brand-specific and general hashtags
        
        3. Product Type:
        - Determine the product category (e.g., Skincare, Makeup, Food, etc.)
        
        4. Skin Type (if applicable):
        - For beauty/skincare products, suggest target skin types
        
        5. Key Ingredients (if applicable):
        - Highlight main ingredients for beauty/skincare products
        
        6. How to Use:
        - Provide clear usage instructions for influencers
        
        Format the response as JSON with these exact keys:
        {
          "tone": "string",
          "messageGuide": "string",
          "hashtags": ["string"],
          "productType": "string",
          "skinType": "string",
          "keyIngredients": "string",
          "howToUse": "string"
        }
      `;

      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "You are a marketing expert specializing in UGC influencer campaigns. Generate engaging, authentic content that influencers can easily follow."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      });

      const response = completion.choices[0].message.content;
      
      // Try to parse JSON response
      try {
        return JSON.parse(response);
      } catch (parseError) {
        // If JSON parsing fails, return structured data
        return {
          tone: "Engaging and authentic",
          messageGuide: "Focus on the product benefits and personal experience",
          hashtags: ["#product", "#review", "#recommendation"],
          productType: "General",
          skinType: "",
          keyIngredients: "",
          howToUse: "Follow product instructions"
        };
      }

    } catch (error) {
      console.error('Error generating campaign content:', error);
      throw new Error('Failed to generate campaign content');
    }
  }

  async generateCampaignFromURL(productURL) {
    try {
      // Step 1: Scrape the product page
      const scrapedData = await this.scrapeProductPage(productURL);
      
      // Step 2: Generate campaign content using AI
      const generatedContent = await this.generateCampaignContent(scrapedData);
      
      // Step 3: Combine scraped and generated data
      const campaignData = {
        // Step 1: Product Information
        productName: scrapedData.title || '',
        productDescription: scrapedData.description || '',
        category: this.detectCategory(scrapedData.title, scrapedData.description),
        brandName: scrapedData.brandName || '',
        
        // Step 2: Campaign Brief
        tone: generatedContent.tone || '',
        messageGuide: generatedContent.messageGuide || '',
        hashtags: generatedContent.hashtags || [],
        
        // Step 3: Product Details
        productType: generatedContent.productType || '',
        skinType: generatedContent.skinType || '',
        keyIngredients: generatedContent.keyIngredients || scrapedData.ingredients || '',
        howToUse: generatedContent.howToUse || scrapedData.usage || '',
        
        // Metadata
        scrapedURL: productURL,
        generatedAt: new Date().toISOString()
      };

      return campaignData;

    } catch (error) {
      console.error('Error in generateCampaignFromURL:', error);
      throw error;
    }
  }

  detectCategory(title, description) {
    const text = (title + ' ' + description).toLowerCase();
    
    if (text.includes('skincare') || text.includes('serum') || text.includes('moisturizer') || text.includes('cleanser')) {
      return 'Skincare';
    }
    if (text.includes('makeup') || text.includes('cosmetic') || text.includes('foundation') || text.includes('lipstick')) {
      return 'Makeup';
    }
    if (text.includes('food') || text.includes('snack') || text.includes('drink') || text.includes('beverage')) {
      return 'Food & Beverage';
    }
    if (text.includes('supplement') || text.includes('vitamin') || text.includes('health')) {
      return 'Health & Wellness';
    }
    if (text.includes('clothing') || text.includes('fashion') || text.includes('apparel')) {
      return 'Fashion';
    }
    
    return 'General';
  }
}

module.exports = new AIGenerateService(); 