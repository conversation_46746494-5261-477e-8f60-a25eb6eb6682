// services/analyzeContent.js
const urlRegex = /https?:\/\/(www\.)?(tiktok\.com|instagram\.com)\/.+/i;

function detectPlatform(url) {
  if (/tiktok\.com/i.test(url)) return "tiktok";
  if (/instagram\.com/i.test(url)) return "instagram";
  return null;
}

function validateThreeUrls(urls = []) {
  if (!Array.isArray(urls) || urls.length !== 3) return "You must submit exactly 3 URLs.";
  const cleaned = urls.map((u) => (u || "").trim());
  if (new Set(cleaned).size !== 3) return "Duplicate URLs are not allowed.";
  for (const u of cleaned) {
    if (!urlRegex.test(u)) return "Please enter a valid TikTok/Instagram URL.";
  }
  return null;
}

// naive keyword extraction/tagging (placeholder)
function extractTagsFromText(text = "") {
  const base = text.toLowerCase();
  const picks = [];
  ["vegan","asmr","review","emotional","informative","natural light","skincare","beauty","food","beverage"]
    .forEach((k) => { if (base.includes(k)) picks.push(k); });
  return Array.from(new Set(picks));
}

function classifyStyleFromTags(tags = []) {
  const styles = [];
  if (tags.some(t => /review|asmr/.test(t))) styles.push("review");
  if (tags.some(t => /emotional/.test(t))) styles.push("emotional");
  if (tags.some(t => /informative|tutorial/.test(t))) styles.push("informative");
  return styles.length ? styles : ["mixed"];
}

function fakeFetchEngagement(url) {
  // Replace with real scrapers/APIs
  const n = Math.floor(Math.random() * 10000);
  const c = Math.floor(Math.random() * 500);
  const duration = 8 + Math.floor(Math.random() * 40);
  return { likes: n, comments: c, durationSec: duration };
}

function analyzeSingle(url) {
  const platform = detectPlatform(url);
  const metrics = fakeFetchEngagement(url);
  const text = url; // placeholder: use captions/hashtags you fetch
  const tags = extractTagsFromText(text);
  const styleLabels = classifyStyleFromTags(tags);
  const detectedProduct = /mask|serum|cream|lipstick|food|drink/i.test(text);
  const detectedCta = /try this|link in bio|buy now|shop/i.test(text);
  return {
    url,
    platform,
    analyzed: true,
    metrics,
    tags,
    styleLabels,
    detectedProduct,
    detectedCta,
  };
}

function aggregateProfile(urlItems = []) {
  const allTags = new Set();
  let cta = false;
  let avgLen = 0;

  urlItems.forEach((u) => {
    (u.tags || []).forEach(t => allTags.add(t));
    if (u.detectedCta) cta = true;
    avgLen += (u.metrics?.durationSec || 0);
  });
  avgLen = urlItems.length ? Math.round(avgLen / urlItems.length) : 0;

  // crude category inference
  const tagsArr = [...allTags];
  const cats = [];
  if (tagsArr.some(t => /skincare|beauty/.test(t))) cats.push("Beauty");
  if (tagsArr.some(t => /food|beverage|mukbang/.test(t))) cats.push("Food","Beverage");

  // base score
  let contentScore = 60;
  if (avgLen >= 10) contentScore += 10;
  if (tagsArr.length >= 3) contentScore += 5;
  if (cta) contentScore += 5;
  contentScore = Math.min(100, contentScore);

  return {
    predictedCategory: Array.from(new Set(cats)),
    creatorTags: tagsArr,
    contentScore,
    hasCta: cta,
  };
}

module.exports = {
  detectPlatform,
  validateThreeUrls,
  analyzeSingle,
  aggregateProfile,
};
