// services/scoring.js
const dayjs = require("dayjs");

const T0 = process.env.T0_ISO || "2025-01-01T00:00:00.000Z";

function scoreForCampaign(creatorProfile, campaign) {
  let score = 0;
  const reasons = [];

  const brandKeywords = (campaign.keywords || []).map(s => s.toLowerCase());
  const brandCats = (campaign.categories || []).map(s => s.toLowerCase());
  const tags = (creatorProfile.creatorTags || []).map(s => s.toLowerCase());
  const cats = (creatorProfile.predictedCategory || []).map(s => s.toLowerCase());

  // +30 exact keyword ↔ tag match
  const kwMatches = brandKeywords.filter(k => tags.includes(k));
  if (kwMatches.length) { score += 30; reasons.push(`Matched keywords: ${kwMatches.slice(0,3).join(", ")}`); }

  // +20 category match
  const catMatches = brandCats.filter(c => cats.includes(c));
  if (catMatches.length) { score += 20; reasons.push(`Category match: ${catMatches.join(", ")}`); }

  // +10 content score ≥ 75
  if ((creatorProfile.contentScore || 0) >= 75) { score += 10; reasons.push("High content score (≥75)"); }

  // Upload rate (0–20), Approval rate (0–15), Satisfaction (0–15) — post T0
  const sinceT0 = creatorProfile.metricsSinceT0 || {};
  score += clamp(sinceT0.uploadRate || 0, 0, 20);
  score += clamp(sinceT0.approvalRate || 0, 0, 15);
  score += clamp(sinceT0.satisfactionScore || 0, 0, 15);

  if ((sinceT0.recentActivityScore || 0) > 0) {
    score += clamp(sinceT0.recentActivityScore, 0, 10);
    reasons.push("Recent activity in last 30 days");
  }

  if (creatorProfile.hasCta) { score += 5; reasons.push("CTA present"); }

  // New participant bonus
  const isNew = creatorProfile.group === "B";
  if (campaign.prioritizeNewCreators && isNew) {
    score += 10;
    reasons.push("New participant bonus (priority)");
  } else if (isNew) {
    score += 5;
    reasons.push("New participant bonus");
  }

  return { score, reasons };
}

function clamp(n, min, max) { return Math.max(min, Math.min(max, n)); }

module.exports = { scoreForCampaign, T0 };
