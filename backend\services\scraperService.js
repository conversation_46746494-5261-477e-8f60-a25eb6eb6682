const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');

puppeteer.use(StealthPlugin());

// Helper: Convert '1.5k', '2M' to numbers
function parseCompactNumber(text) {
  if (!text) return 0;
  const num = text.toLowerCase().replace(/,/g, '').trim();
  if (num.endsWith('k')) return parseFloat(num) * 1_000;
  if (num.endsWith('m')) return parseFloat(num) * 1_000_000;
  if (num.endsWith('b')) return parseFloat(num) * 1_000_000_000;
  return parseFloat(num) || 0;
}

async function scrapeContentMetrics(content_url) {
  console.log("🔍 Scraping URL:", content_url);
  if (!content_url) throw new Error('Content URL is required');

  const isInstagram = content_url.includes('instagram.com');
  const isTikTok = content_url.includes('tiktok.com');
  const browser = await puppeteer.launch({
    headless: 'new',
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-blink-features=AutomationControlled',
      '--disable-infobars',
      '--window-size=1280,800'
    ]
  });

  const page = await browser.newPage();

  try {
    await page.setViewport({ width: 1280, height: 800 });
    await page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/115.0.0.0 Safari/537.36'
    );

    await page.goto(content_url, { waitUntil: 'networkidle2', timeout: 60000 });
    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for DOM updates

    let metrics = { views: 0, likes: 0, comments: 0 };

    if (isInstagram) {
      try {
        await page.waitForSelector('span', { timeout: 10000 });

        const { likesText, viewsText } = await page.evaluate(() => {
          const spans = Array.from(document.querySelectorAll('span'));
          let likesText = null, viewsText = null;

          for (const span of spans) {
            const text = span.innerText.trim().toLowerCase();
            if (text.endsWith('likes') && !likesText) {
              const inner = span.querySelector('span');
              likesText = inner ? inner.innerText.trim() : null;
            } else if (text.endsWith('views') && !viewsText) {
              const inner = span.querySelector('span');
              viewsText = inner ? inner.innerText.trim() : null;
            }
          }
          return { likesText, viewsText };
        });

        metrics.likes = parseCompactNumber(likesText);
        metrics.views = parseCompactNumber(viewsText);


       // Count total comment items <li> across all comment wrappers
  const totalComments = await page.evaluate(() => {
    const wrappers = Array.from(document.querySelectorAll('div > div > ul')); // find all <ul> inside wrappers
    let count = 0;

    wrappers.forEach(ul => {
      const li = ul.querySelector('div[role="button"] > li');
      if (li) count += 1;
    });

    return count;
  });
        metrics.comments = totalComments;

        console.log("✅ Metrics extracted via selectors:", metrics);

      } catch (selectorErr) {
        console.warn('⚠️ Selector extraction failed, using fallback:', selectorErr.message);

        try {
          const pageContent = await page.content();
          const likesMatch = pageContent.match(/"likeCount":"?(\d+)"?/);
          const commentsMatch = pageContent.match(/"commentCount":"?(\d+)"?/);
          const viewsMatch = pageContent.match(/"playCount":"?(\d+)"?/);

          metrics.likes = likesMatch ? parseInt(likesMatch[1], 10) : 0;
          metrics.comments = commentsMatch ? parseInt(commentsMatch[1], 10) : 0;
          metrics.views = viewsMatch ? parseInt(viewsMatch[1], 10) : 0;

          console.log("✅ Fallback metrics via regex:", metrics);
        } catch (regexErr) {
          console.error('❌ Regex fallback failed:', regexErr.message);
        }
      }
    }else if (isTikTok) {
  try {
    await page.waitForSelector('strong[data-e2e="like-count"]', { timeout: 10000 });

    const { likesText, commentsText, viewsText } = await page.evaluate(() => {
      const parseSafeText = (selector) => {
        const el = document.querySelector(selector);
        return el ? el.innerText.trim() : null;
      };

      const likesText = parseSafeText('strong[data-e2e="like-count"]');
      const commentsText = parseSafeText('strong[data-e2e="comment-count"]');
      const viewsText = parseSafeText('span[data-e2e="video-views"]'); // fallback, may be null

      return { likesText, commentsText, viewsText };
    });

    metrics.likes = parseCompactNumber(likesText);
    metrics.comments = parseCompactNumber(commentsText);
    metrics.views = parseCompactNumber(viewsText);

    console.log("✅ TikTok metrics extracted via selectors:", metrics);

  } catch (selectorErr) {
    console.warn('⚠️ TikTok selector failed, using fallback:', selectorErr.message);

    try {
      const pageContent = await page.content();

      const likesMatch = pageContent.match(/"diggCount":(\d+)/);
      const commentsMatch = pageContent.match(/"commentCount":(\d+)/);
      const viewsMatch = pageContent.match(/"playCount":(\d+)/);

      metrics.likes = likesMatch ? parseInt(likesMatch[1], 10) : 0;
      metrics.comments = commentsMatch ? parseInt(commentsMatch[1], 10) : 0;
      metrics.views = viewsMatch ? parseInt(viewsMatch[1], 10) : 0;

      console.log("✅ TikTok fallback metrics via regex:", metrics);

    } catch (regexErr) {
      console.error('❌ TikTok regex fallback failed:', regexErr.message);
    }
  }
}
 else {
      throw new Error('Unsupported platform URL');
    }

    return { ...metrics, last_updated: new Date() };

  } catch (err) {
    console.error(`❌ Scraper error for ${content_url}:`, err.message);
    throw err;
  } finally {
    await browser.close();
  }
}

async function scrapeWithRetry(url, retries = 2) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await scrapeContentMetrics(url);
    } catch (err) {
      console.warn(`⚠️ Attempt ${attempt} failed: ${err.message}`);
      if (attempt === retries) {
        console.error(`❌ All ${retries} attempts failed for ${url}`);
        return null;
      }
    }
  }
}

module.exports = { scrapeContentMetrics, scrapeWithRetry };
