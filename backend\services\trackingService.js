// services/trackingService.js
const axios = require('axios');
const registerTracking = async (trackingNumber) => {
  const token = "84D0A824E840847AC7405C10A9C83806";

  try {
    const response = await axios.post(
      'https://api.17track.net/track/v2.2/register',
      [
        { number: trackingNumber }  // Note: array of objects with 'number' key
      ],
      {
        headers: {
          '17token': token,
          'Content-Type': 'application/json'
        }
      }
    );
    console.log('Response from 17TRACK:', response.data);
    return response.data;  // return response if needed

  } catch (error) {
    console.error('Error registering tracking number:', error.response?.data || error.message);
    throw error;
  }
};


const getTrackingInfo = async (trackingNumber) => {
  const token = "84D0A824E840847AC7405C10A9C83806";
  const response = await axios.post('https://api.17track.net/track/v2.2/gettrackinfo', {
    numbers: [trackingNumber]
  }, {
    headers: {
      '17token': token,
      'Content-Type': 'application/json'
    }
  });
  return response.data;
};

module.exports = { registerTracking, getTrackingInfo };
