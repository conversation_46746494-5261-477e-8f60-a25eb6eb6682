const axios = require('axios');

async function testAIEndpoint() {
  try {
    console.log('🧪 Testing AI Generate endpoint...');
    
    // Test health endpoint
    const healthResponse = await axios.get('http://localhost:2340/health');
    console.log('✅ Health endpoint working:', healthResponse.data);
    
    // Test AI health endpoint
    const aiHealthResponse = await axios.get('http://localhost:2340/api/ai/health');
    console.log('✅ AI health endpoint working:', aiHealthResponse.data);
    
    console.log('🎉 All endpoints are working!');
    
  } catch (error) {
    console.error('❌ Error testing endpoints:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the server is running on port 2340');
    }
  }
}

testAIEndpoint(); 