#!/usr/bin/env node

// Test script to verify deployment fixes
const axios = require('axios');

const BASE_URL = process.env.TEST_URL || 'http://localhost:5000';

async function testEndpoints() {
  console.log('🧪 Testing Deployment Fixes');
  console.log('============================');
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log('');

  let allPassed = true;

  // Test 1: Health Check
  console.log('🏥 Test 1: Health Check Endpoint');
  try {
    const response = await axios.get(`${BASE_URL}/health`, { timeout: 10000 });
    if (response.status === 200 && response.data.status === 'healthy') {
      console.log('   ✅ Health check passed');
      console.log(`   📊 Status: ${response.data.status}`);
      console.log(`   🕐 Timestamp: ${response.data.timestamp}`);
      console.log(`   🔌 Port: ${response.data.port}`);
      console.log(`   🌍 Environment: ${response.data.environment}`);
    } else {
      console.log('   ❌ Health check failed');
      console.log('   📄 Response:', response.data);
      allPassed = false;
    }
  } catch (error) {
    console.log('   ❌ Health check failed');
    console.log(`   🚨 Error: ${error.message}`);
    allPassed = false;
  }

  console.log('');

  // Test 2: Root Endpoint
  console.log('🏠 Test 2: Root Endpoint');
  try {
    const response = await axios.get(`${BASE_URL}/`, { timeout: 10000 });
    if (response.status === 200 && response.data.app) {
      console.log('   ✅ Root endpoint passed');
      console.log(`   📱 App: ${response.data.app}`);
      console.log(`   🕐 Timestamp: ${response.data.timestamp}`);
    } else {
      console.log('   ❌ Root endpoint failed');
      console.log('   📄 Response:', response.data);
      allPassed = false;
    }
  } catch (error) {
    console.log('   ❌ Root endpoint failed');
    console.log(`   🚨 Error: ${error.message}`);
    allPassed = false;
  }

  console.log('');

  // Test 3: CORS Headers
  console.log('🌐 Test 3: CORS Configuration');
  try {
    const response = await axios.options(`${BASE_URL}/health`, {
      headers: {
        'Origin': 'https://guideway-consulting.vercel.app',
        'Access-Control-Request-Method': 'GET'
      },
      timeout: 10000
    });
    
    if (response.status === 200 || response.status === 204) {
      console.log('   ✅ CORS preflight passed');
      console.log(`   🔗 Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin'] || 'Not set'}`);
    } else {
      console.log('   ❌ CORS preflight failed');
      allPassed = false;
    }
  } catch (error) {
    console.log('   ❌ CORS test failed');
    console.log(`   🚨 Error: ${error.message}`);
    allPassed = false;
  }

  console.log('');

  // Test 4: Rate Limiting (Health Check Exemption)
  console.log('⚡ Test 4: Rate Limiting Exemption for Health Check');
  try {
    const promises = [];
    for (let i = 0; i < 5; i++) {
      promises.push(axios.get(`${BASE_URL}/health`, { timeout: 5000 }));
    }
    
    const responses = await Promise.all(promises);
    const allSuccessful = responses.every(r => r.status === 200);
    
    if (allSuccessful) {
      console.log('   ✅ Health check rate limiting exemption works');
      console.log('   📊 Multiple rapid requests succeeded');
    } else {
      console.log('   ❌ Rate limiting exemption failed');
      allPassed = false;
    }
  } catch (error) {
    console.log('   ❌ Rate limiting test failed');
    console.log(`   🚨 Error: ${error.message}`);
    allPassed = false;
  }

  console.log('');

  // Summary
  console.log('📋 Test Summary');
  console.log('===============');
  if (allPassed) {
    console.log('✅ All tests passed! Deployment fixes are working correctly.');
    console.log('🚀 Ready for Railway deployment.');
  } else {
    console.log('❌ Some tests failed. Please check the issues above.');
    console.log('🔧 Fix the issues before deploying to Railway.');
  }

  console.log('');
  console.log('🔗 Next Steps:');
  console.log('1. If all tests pass locally, deploy to Railway');
  console.log('2. Set environment variables in Railway dashboard');
  console.log('3. Test the deployed health check endpoint');
  console.log('4. Monitor Railway logs for any issues');

  process.exit(allPassed ? 0 : 1);
}

// Run tests
testEndpoints().catch(error => {
  console.error('🚨 Test runner failed:', error.message);
  process.exit(1);
});
