// File: utils/aiUtils.js

const { OpenAI } = require('openai');

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: "********************************************************************************************************************************************************************",
});


// Step 1: Classify Product Category
function classifyCategory(data) {
  const { description = '', ingredients = '' } = data;
  const text = `${description} ${ingredients}`.toLowerCase();

  if (/tea|coffee|juice|drink/.test(text)) return "Beverage";
  if (/cream|lotion|serum|skincare|makeup/.test(text)) return "Beauty";
  if (/vitamin|supplement|probiotic|collagen/.test(text)) return "Wellness & Supplements";
  if (/snack|food|meal|bar|chocolate/.test(text)) return "Food";
  if (/soap|deodorant|shampoo|body wash/.test(text)) return "Personal Care";

  return "Beauty";  // Default fallback
}


// Step 3: Generate AI Prompt
function cleanProductName(name) {
  if (!name) return "";

  return name
    // Remove anything after comma, dash, or parentheses
    .split(/,|-|\(|\[/)[0]

    // Remove units like ml, fl.oz, g, pcs, pack, etc.
    .replace(/\b\d+(\.\d+)?\s*(ml|fl\.oz|g|pcs?|packs?)\b/gi, "")

    // Remove multiple spaces
    .replace(/\s{2,}/g, " ")

    // Trim leading/trailing spaces
    .trim();
}


function formatBrandHandle(brand) {
  return "@" + brand.toLowerCase().replace(/\\s+/g, "");
}

// Step 1: Keyword-category map
const keywordCategoryMap = {
  Beauty: ["shampoo", "sunscreen", "moisturizer", "serum", "lip balm", "toner", "cleanser", "face wash"],
  Food: ["snack", "granola", "bar", "pasta", "cookie", "soup", "cereal"],
  Beverage: ["coffee", "tea", "juice", "smoothie", "soda", "drink", "energy drink", "hydration"],
  "Wellness & Supplements": ["vitamin", "protein", "omega", "supplement", "gummy", "capsule", "multivitamin"],
  "Personal Care": ["deodorant", "soap", "body wash", "toothpaste", "lotion", "hand wash", "body lotion"]
};

// Helper: Cleans product name (stub)
function cleanProductName(name) {
  return name.trim();
}

// Helper: Converts brand name to social handle
function formatBrandHandle(brand) {
  return "@" + brand.replace(/\s+/g, "").toLowerCase();
}

// Keyword matching to override AI's category
function overrideCategory(data, originalCategory) {
  const text = `${data.productName} ${data.description}`.toLowerCase();

  for (const [category, keywords] of Object.entries(keywordCategoryMap)) {
    for (const keyword of keywords) {
      if (text.includes(keyword.toLowerCase())) {
        return category;  // Override if keyword matched
      }
    }
  }

  return originalCategory;  // No match; use original category
}

// Main: generatePrompt returns { prompt, category }
function generatePrompt(data, aiPredictedCategory) {
  const shortName = cleanProductName(data.productName);
  const shortNameLower = shortName;
  const brandHandle = formatBrandHandle(data.brand);

  // Override category
  const finalCategory = overrideCategory(data, aiPredictedCategory);

  const baseFields = `
Product Name: ${shortName}
Description: ${data.description}
Ingredients: ${data.ingredients}
Usage: ${data.usage || "N/A"}
Brand Name: ${data.brand}
`;

  const prompts = {
    Beauty: `You are generating a UGC (User-Generated Content) campaign copy for a skincare product, based on the details provided below. Your goal is to write copy that appeals to Gen Z and Millennial skincare consumers who are active on social media and value clean, trustworthy beauty content.

${baseFields}

Instructions:
- Use natural, trustworthy language – avoid exaggeration or generic claims.
- Keep the tone friendly yet professional.
- Do not use emojis.
- Mention the product’s key ingredients along with their skincare benefits, using common mappings (e.g., CICA = calming, Niacinamide = brightening, etc.).
- Automatically convert the brand name into a social handle (e.g., Esther Formula → @estherformula).
- Keep the main UGC copy between 80–120 words.

Return JSON:
{
  "product_type": "skincare",
  "product_name": "${shortNameLower}",
  "brand": "${data.brand}",
  "mention": "${brandHandle}",
  "ugc_copy": "...",
  "hashtags": ["#skincare", "#glowup", "#cleanbeauty", "#genzskin", "#dailyglow"],
  "tone_guide": "The tone is friendly, confident, and informative — designed to resonate with Gen Z and Millennial skincare consumers who value ingredient transparency and real results.",
  "ingredients": [
    { "name": "CICA", "effect": "calming" },
    { "name": "Niacinamide", "effect": "brightening" },
    { "name": "Hyaluronic Acid", "effect": "hydrating" }
  ],
  "usage": "${data.usage || ""}"
}`,

    Food: `Here is a food product detail:

${baseFields}

Instructions:
- Use friendly, appetizing language.
- Highlight dietary benefits and convenience.
- Target Gen Z and Millennials.

Return JSON:
{
  "product_type": "food",
  "product_name": "${shortNameLower}",
  "brand": "${data.brand}",
  "mention": "${brandHandle}",
  "ugc_copy": "...",
  "hashtags": ["#healthyfood", "#quickmeals", "#foodie", "#snacktime", "#cleaneating"],
  "tone_guide": "Light, friendly, and appetizing. Emphasize convenience and lifestyle.",
  "preparation_method": "Ready to eat / Cook for 5 mins / Add water etc.",
  "dietary_tags": ["vegan", "gluten-free", "high-protein"],
  "eating_scene": "Ideal for breakfast / party snack / office lunch etc."
}`,

    Beverage: `Here is a beverage product detail:

${baseFields}

Instructions:
- Use refreshing and engaging language.
- Highlight how to enjoy the beverage and any dietary benefits.
- Keep it relatable to young consumers.

Return JSON:
{
  "product_type": "beverage",
  "product_name": "${shortNameLower}",
  "brand": "${data.brand}",
  "mention": "${brandHandle}",
  "ugc_copy": "...",
  "hashtags": ["#drinkfresh", "#energydrink", "#hydration", "#coffeetime", "#teaaddict"],
  "tone_guide": "Energetic, cozy, and bold. Appeals to young, lifestyle-focused consumers.",
  "serving_type": "Bottle / Can / Powder etc.",
  "serving_temperature": "Chilled / Hot / Room temp",
  "caffeine_content": "High / Low / Caffeine-free",
  "dietary_tags": ["vegan", "sugar-free", "low-calorie"]
}`,

    "Wellness & Supplements": `Product info for a wellness supplement:

${baseFields}

Instructions:
- Avoid medical claims. Highlight feel-good benefits.
- Emphasize function, flavor, usage timing.
- Tone should be motivational and health-conscious.

Return JSON:
{
  "product_type": "supplement",
  "product_name": "${shortNameLower}",
  "brand": "${data.brand}",
  "mention": "${brandHandle}",
  "ugc_copy": "...",
  "hashtags": ["#wellnessjourney", "#healthgoals", "#dailyvitamin", "#energize", "#selfcare"],
  "tone_guide": "Motivational, health-conscious, and trustworthy. Designed for mindful living.",
  "supplement_type": "Vitamin C / Protein Powder / Omega 3 etc.",
  "usage_guidelines": "Take 1 daily / After meal / Before workout etc.",
  "flavor": "Berry / Citrus / Unflavored",
  "form_type": "Capsule / Powder / Gummy",
  "target_function": ["Immunity", "Energy", "Focus"],
  "dietary_tags": ["gluten-free", "vegan", "non-GMO"]
}`,

    "Personal Care": `Product details for a personal care item:

${baseFields}

Instructions:
- Keep tone practical, clean, and confident.
- Emphasize daily use, scent, and skin feel.
- Mention key ingredients and texture.

Return JSON:
{
  "product_type": "personal care",
  "product_name": "${shortNameLower}",
  "brand": "${data.brand}",
  "mention": "${brandHandle}",
  "ugc_copy": "...",
  "hashtags": ["#dailycare", "#freshfeel", "#cleangrooming", "#skincareroutine", "#bodycare"],
  "tone_guide": "Practical, soothing, and refreshing. Appeals to everyday routines.",
  "skin_body_areas": ["Hands", "Underarms", "Face", "Body"],
  "key_ingredients": ["Shea Butter", "Vitamin E", "Aloe Vera"],
  "scent_flavor": "Lavender / Citrus / Unscented",
  "texture_form": "Cream / Gel / Foam",
  "usage": "${data.usage || ""}"
}`
  };

  // Return both prompt string and final category
  return {
    prompt: prompts[finalCategory] || prompts["Beauty"],
    category: finalCategory
  };
}






// Step 4b: Call OpenAI
async function callOpenAI(prompt) {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",  // Change to gpt-4 if needed
      temperature: 0.7,
      max_tokens: 1000,
      messages: [{ role: "user", content: prompt }],
    });

    const text = response.choices[0].message.content.trim();
    const jsonMatch = text.match(/{[\s\S]*}/);
    if (!jsonMatch) throw new Error("No valid JSON found in OpenAI response");

    return JSON.parse(jsonMatch[0]);
  } catch (error) {
    console.error("OpenAI API Error:", error.message);
    throw error;
  }
}

// Step 5: Map AI Response + Scraped Data to Campaign Object
function mapResponseToCampaign(aiResponse, category, scrapedData) {

  console.log(aiResponse)
  const baseCampaign = {
   campaignTitle: `${aiResponse.product_name || cleanProductName(scrapedData.productName)} UGC Campaign`,
    category,
    brandName: aiResponse.brand || scrapedData.brand || "",
    productName: aiResponse.product_name || scrapedData.productName || "",
    productDescription: aiResponse.description || scrapedData.description || "",
    requiredHashtags: aiResponse.hashtags || [],
    mentionHandle: aiResponse.mention || "",
    toneMessage: "",
    categoryDetails: {}, // Fill below
  };

  // Map category-specific details
  switch (category) {
    case "Beauty":
      baseCampaign.categoryDetails[category] = {
        productType: aiResponse.product_type || "",
        skinTypes: aiResponse.skin_types || [], // If AI can return it
        keyIngredients: (aiResponse.ingredients || [])
          .map(i => i.name)
          .join(", "),
        howToUse: aiResponse.usage || scrapedData.usage || "",
      };
      break;

    case "Food":
      baseCampaign.categoryDetails[category] = {
        preparationMethod: aiResponse.preparation_method || "",
        dietaryTags: aiResponse.dietary_tags || [],
        eatingScene: aiResponse.eating_scene || "",
      };
      break;

    case "Beverage":
      baseCampaign.categoryDetails[category] = {
        servingType: aiResponse.beverage_type || "",
        servingTemperature: aiResponse.serving_suggestion || "",
        caffeineContent: aiResponse.caffeine_content || "", // optional
        dietaryTags: aiResponse.dietary_tags || [],
      };
      break;

    case "Wellness & Supplements":
      baseCampaign.categoryDetails[category] = {
        productType: aiResponse.supplement_type || "",
        targetFunction: aiResponse.benefits || [],
        formType: aiResponse.form_type || "",
        usageTiming: aiResponse.usage_guidelines || "",
        flavor: aiResponse.flavor || "",
        dietaryTags: aiResponse.dietary_tags || [],
      };
      break;

    case "Personal Care":
      baseCampaign.categoryDetails[category] = {
        productType: aiResponse.product_type || "",
        skinBodyAreas: aiResponse.body_areas || [],
        keyIngredients: (aiResponse.ingredients || [])
          .map(i => i.name)
          .join(", "),
        scentFlavor: aiResponse.scent || "",
        textureForm: aiResponse.texture || "",
      };
      break;

    default:
      baseCampaign.categoryDetails[category] = {}; // fallback
  }

  return baseCampaign;
}


// Export functions
module.exports = {
  classifyCategory,
  cleanProductName,
  generatePrompt,
  callOpenAI,
  mapResponseToCampaign,
};
