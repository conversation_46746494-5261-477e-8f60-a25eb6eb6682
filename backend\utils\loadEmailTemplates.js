// 📂 utils/loadEmailTemplates.js
const ejs = require('ejs');
const path = require('path');
const emailBodies = require('../templates/emailBodies');

function getEmailBody(templateName, variables = {}) {
  const template = emailBodies.find(t => t.name === templateName);
  if (!template) {
    throw new Error(`Template "${templateName}" not found.`);
  }

  try {
    return ejs.render(template.body, variables); // Renders correctly
  } catch (err) {
    console.error(`Error rendering template "${templateName}":`, err);
    throw err;
  }
}

module.exports = { getEmailBody };
