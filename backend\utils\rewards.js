// Tiers per product doc (new system)
export const TIERS = [
  { tier: 1,  totalReward: 10, label: "$10" },
  { tier: 3,  totalReward: 30, label: "$30 total" },  // unlock adds +$20 bonus at 3
  { tier: 5,  totalReward: 60, label: "$60 total" },  // unlock adds +$30 bonus at 5
  { tier: 10, totalReward: 110, label: "$110 total"}  // unlock adds +$50 bonus at 10
];

// Given total approvals N, calculate total cumulative reward owed.
export const cumulativeTotal = (n) => {
  let total = 0;
  if (n >= 1) total += 10;
  if (n >= 3) total += 20;
  if (n >= 5) total += 30;
  if (n >= 10) total += 50;
  if (n > 10) total += (n - 10) * 5;
  return total;
};

// Determine newly unlocked tiers vs already claimed
export const unlockedTiers = (n) => TIERS.filter(t => n >= t.tier).map(t => t.tier);

// Map status for UI cards
export const cardStatuses = (n, claimed) => {
  const unlocked = new Set(unlockedTiers(n));
  return TIERS.map(t => ({
    id: t.tier,
    reward: t.label,
    status: claimed.includes(t.tier) ? "claimed"
           : unlocked.has(t.tier)    ? "in_progress"
           : "locked"
  })).concat([{ id: 11, reward: "+$5 per post", status: n > 10 ? "ongoing" : "locked" }]);
};
