// File: utils/scraper.js
const axios = require('axios');
const cheerio = require('cheerio');

// Step 1 + 2: Scrape Page and Extract Product Info
async function scrapeProductPage(url) {
  try {
    const { data } = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9',
      }
    });

    const $ = cheerio.load(data);

    // Product Name (Amazon-specific selector; fallback to h1)
    const productName = $('#productTitle').text().trim() || $('h1').first().text().trim();

    // Description from feature bullets (join bullets for clarity)
    const description = $('#feature-bullets')
      .find('li')
      .map((i, el) => $(el).text().trim())
      .get()
      .join(' | ') || $('meta[name="description"]').attr('content') || '';

    // Ingredients and Usage from #important-information (Amazon structure)
    const importantInfo = $('#important-information').text().trim();
    let ingredients = '';
    let usage = '';

    if (importantInfo) {
      const ingredientsMatch = importantInfo.match(/Ingredients[:\s]*([\s\S]*?)(Directions|$)/i);
      const usageMatch = importantInfo.match(/(Directions|How to Use)[:\s]*([\s\S]*?)(Ingredients|$)/i);

      ingredients = ingredientsMatch ? ingredientsMatch[1].trim().replace(/\s+/g, ' ') : '';
      usage = usageMatch ? usageMatch[2].trim().replace(/\s+/g, ' ') : '';
    }

    // Brand Name
    let brand = $('#bylineInfo').text().trim() || $('meta[property="og:site_name"]').attr('content') || "Unknown";
brand = brand.replace(/^Brand:\s*/i, '')                   // remove "Brand: "
             .replace(/^Visit the\s+/i, '')                // remove "Visit the"
             .replace(/Store$/i, '')                       // remove "Store" at end
             .trim();
    // Optional: extra metadata (texture, flavor, temperature, etc.)
    const extraMeta = {};
    $('meta').each((i, el) => {
      const name = $(el).attr('name') || $(el).attr('property');
      const content = $(el).attr('content');
      if (name && content) {
        if (/texture|flavor|temperature/i.test(name)) {
          extraMeta[name] = content;
        }
      }
    });

    // Step 3: Classify Category
    const category = classifyCategory({ description, ingredients });

    return { productName, description, ingredients, usage, brand, extraMeta, category };

  } catch (error) {
    console.error('Scraping failed:', error.message);
    return {
      productName: '',
      description: '',
      ingredients: '',
      usage: '',
      brand: 'Unknown',
      extraMeta: {},
      category: 'Beauty'
    };
  }
}

// Step 3: Category Classifier (Regex-based)
function classifyCategory({ description = '', ingredients = '' }) {
  const text = `${description} ${ingredients}`.toLowerCase();

  if (/tea|coffee|juice|drink|soda/.test(text)) return 'Beverage';
  if (/cream|lotion|serum|skincare|makeup/.test(text)) return 'Beauty';
  if (/vitamin|supplement|probiotic|collagen/.test(text)) return 'Wellness & Supplements';
  if (/snack|food|meal|protein bar|chocolate/.test(text)) return 'Food';
  if (/soap|deodorant|shampoo|body wash/.test(text)) return 'Personal Care';

  return 'Beauty'; // Default fallback
}

module.exports = { scrapeProductPage };
