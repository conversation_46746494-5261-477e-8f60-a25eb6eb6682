// ================================
// sendEmail.js
// ================================
const ejs = require('ejs');
const dotenv = require("dotenv");
const path = require('path');
const nodemailer = require('nodemailer');
const { getEmailBody } = require('../utils/loadEmailTemplates');
dotenv.config();

const {
  SMTP_HOST, SMTP_PORT, SMTP_USER, SMTP_PASS, SMTP_SEND_EMAIL,SEND_EMAIL, BREVO_API_KEY
} = process.env;

// Gmail SMTP (default for everything except welcome/reminder)
const transporter = nodemailer.createTransport({
  host: SMTP_HOST,
  port: Number(SMTP_PORT),
  secure: false, // true for 465, false for other ports
  auth: {
    user: SMTP_USER,
    pass: SMTP_PASS,
  },
});

async function sendEmail({ userName, userEmail, campaignTitle, trackingLink, newDeadline, brandName, templateName, to, subject }) {

   const emailTemplatePath = path.join(process.cwd(), 'templates', 'emailTemplate.ejs');
  const emailBody = getEmailBody(templateName, {
    userName,
    userEmail,
    campaignTitle,
    trackingLink,
    newDeadline,
    brandName
  });
  console.log(emailBody)

  const html = await ejs.renderFile(emailTemplatePath, {
    userName,
    emailSubject: subject,
    emailBody,
    currentYear: new Date().getFullYear()
  });

  const mailOptions = {
    from: SEND_EMAIL,
    to,
    subject,
    html
  };

  await transporter.sendMail(mailOptions);
}

module.exports = sendEmail;