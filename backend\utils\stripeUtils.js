const Stripe = require('stripe');
const { BrandPlan, Brand } = require('../database/index');
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

/**
 * Creates a Stripe Checkout Session for purchase, upgrade, or add-on
 * @param {Object} options
 * @param {string} options.brandId - MongoDB ObjectId of the brand
 * @param {string} options.planId - MongoDB ObjectId of the selected plan
 * @param {boolean} [options.isUpgrade=false] - true if upgrade flow
 * @param {string} [options.upgradeFromPlanId] - original planId for upgrade
 * @param {boolean} [options.isAddon=false] - true if add-on purchase
 * @param {string} [options.subscriptionId] - subscriptionId for add-ons
 * @param {string} [options.addonType] - 'campaign' or 'creator'
 * @param {number} [options.quantity] - number of add-on units
 * @returns {Promise<Stripe.Checkout.Session>}
 */
async function createCheckoutSession({
  brandId,
  planId,
  isUpgrade = false,
  upgradeFromPlanId = null,
  isAddon = false,
  subscriptionId = null,
  addonType = null,
  quantity = 1
}) {
  // Fetch brand and plan
  const plan = await BrandPlan.findById(planId);
  if (!plan) throw new Error('Plan not found');
  const brand = await Brand.findById(brandId);
  if (!brand) throw new Error('Brand not found');

  // Determine price and description
  let unitAmount;
  let description;
  if (isAddon) {
    // Price based on add-on type and quantity
    const pricePerUnit = addonType === 'campaign'
      ? plan.campaignAddonPrice
      : plan.creatorAddonPrice;
    unitAmount = Math.round(pricePerUnit * 100);
    description = `Add-on: ${quantity} ${addonType}(s)`;
  } else {
    // Purchase or upgrade
    unitAmount = Math.round(plan.price * 100);
    description = `${plan.name} Plan${isUpgrade ? ' (Upgrade)' : ''}`;
  }

  // Build metadata
  const metadata = {
    brandId: brandId.toString(),
    planId: planId.toString(),
    isUpgrade: isUpgrade.toString(),
    isAddon: isAddon.toString(),
  };
  if (isUpgrade && upgradeFromPlanId) {
    metadata.upgrade_from = upgradeFromPlanId.toString();
  }
  if (isAddon && subscriptionId && addonType) {
    metadata.subscriptionId = subscriptionId.toString();
    metadata.addonType = addonType;
    metadata.quantity = quantity.toString();
  }

  // Create Stripe Checkout Session
  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    mode: 'payment',
    customer_email: brand.email,
    line_items: [
      {
        price_data: {
          currency: 'usd',
          unit_amount: unitAmount,
          product_data: { name: description },
        },
        quantity: isAddon ? quantity : 1,
      },
    ],
    metadata,
    billing_address_collection: 'required',
    // customer_update: {
    //   address: 'auto',
    //   name: 'auto',
    // },
    success_url: `${process.env.FRONTEND_URL}/brand/payment-success?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.FRONTEND_URL}/brand/payment-cancelled`,
  });

  return session;
}

module.exports = { createCheckoutSession };
