const express = require('express');
const crypto = require('crypto');
const axios = require('axios');
const router = express.Router();

const { campaignSubmission } = require("../database");

const API_KEY = "84D0A824E840847AC7405C10A9C83806"; // Replace with your actual API key

// Emoji mapping for tracking statuses
const statusEmojiMap = {
  1: "❌ Not Found",
  2: "🚚 In Transit",
  3: "✅ Delivered",
  4: "⚠️ Expired",
  5: "❗ Exception",
  6: "❌ Undelivered"
};

// Middleware to parse raw body only for /17track route to validate signature
router.use('/17track', express.raw({ type: '*/*' }));

// Function to stop tracking items via 17track API
const stopTrackingItems = async (items) => {
  if (!Array.isArray(items) || items.length === 0) {
    console.warn("No tracking items provided to stop.");
    return;
  }
  try {
    const response = await axios.post(
      "https://api.17track.net/track/v2.2/stoptrack",
      items,
      {
        headers: {
          "content-type": "application/json",
          "17token": API_KEY,
        },
      }
    );
    console.log("Stop tracking response:", response.data);
    return response.data;
  } catch (err) {
    console.error("Error stopping tracking items:", err.response?.data || err.message);
    throw err;
  }
};

// Verify webhook signature
function isValidSignature(rawBody, signature, apiKey) {
  const expected = crypto
    .createHmac("sha256", apiKey)
    .update(rawBody)
    .digest("base64");
  return expected === signature;
}

// Handle TRACKING_UPDATED event (rich object)
const handleTrackingUpdated = async (data) => {
  const trackingNumber = data.number;
  const carrier = data.carrier;
  const status = data.track_info?.latest_status?.status || "Unknown";
  const latestEvent = data.track_info?.latest_event || {};

  console.log(`[TRACKING_UPDATED] ${trackingNumber}`);
  console.log(`Status: ${status}`);
  console.log(`Latest event:`, latestEvent);

   const submission = await campaignSubmission.findOne({
              'tracking_info.tracking_number': trackingNumber
            });

            if (submission) {
              submission.tracking_info.delivery_status = status || "❔ Unknown";
              submission.tracking_info.last_updated = new Date(latestEvent?.time_utc || Date.now());
              submission.tracking_info.origin_info = data;

              await submission.save();
            }

  // Example: if status is Delivered, stop tracking
  if (status === "Delivered") {
    await stopTrackingItems([{ number: trackingNumber, carrier }]);
  }

};

// Handle TRACKING_STOPPED event
const handleTrackingStopped = async (data) => {
  const trackingNumber = data.number;
  console.log(`[TRACKING_STOPPED] ${trackingNumber}`);
  // TODO: Add DB update logic here if needed
};

router.post('/17track', async (req, res) => {
  const rawBody = req.body; // Buffer from express.raw middleware

  const signature = req.header("X-17TRACK-Signature");
  
  console.log("Signature received:", signature);
  console.log("Raw body buffer valid:", Buffer.isBuffer(rawBody));
  // console.log("Raw body (string):", rawBody.toString());

  // if (!isValidSignature(rawBody, signature, API_KEY)) {
  //   console.warn("Invalid signature");
  //   return res.status(401).send("Invalid signature");
  // }

  let payload;
  try {
    payload = JSON.parse(rawBody.toString("utf8"));
  } catch (err) {
    console.error("Invalid JSON:", err);
    return res.status(400).send("Invalid JSON");
  }

  const event = payload.event;
  const data = payload.data;

  //console.log("Webhook event:", event);
  //console.log("Webhook data:", data);


  try {
    switch (event) {
      case "TRACKING_UPDATED":
        await handleTrackingUpdated(data);
        break;

      case "TRACKING_STOPPED":
        await handleTrackingStopped(data);
        break;

      default:
        console.log(`Unknown event: ${event}`);

        // For backward compatibility, your original code assumes
        // a simpler data structure — let's also check for 'data' array:
        if (Array.isArray(payload.data)) {
          // Your original processing (numeric status code) for older webhook format
          const updates = payload.data;
          for (const update of updates) {
            const {
              number,
              status,
              lastUpdateTime,
              origin_info = {}
            } = update;

            if (!number || typeof status !== 'number') continue;

            const submission = await campaignSubmission.findOne({
              'tracking_info.tracking_number': number
            });

            if (submission) {
              submission.tracking_info.delivery_status = status || "❔ Unknown";
              submission.tracking_info.last_updated = new Date(lastUpdateTime || Date.now());
              submission.tracking_info.origin_info = origin_info;

              await submission.save();
            }
          }
        }
        break;
    }

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return res.sendStatus(500);
  }
});

module.exports = router;
