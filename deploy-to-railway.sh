#!/bin/bash

# 🚂 Railway Deployment Script for Guideway Consulting
echo "🚂 Deploying Guideway Consulting Backend to Railway"
echo "=================================================="
echo ""

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Installing..."
    echo "Please run: npm install -g @railway/cli"
    echo "Then run: railway login"
    exit 1
fi

echo "✅ Railway CLI found"
echo ""

# Navigate to backend directory
cd backend

echo "🔧 Setting up Railway project..."
echo ""

# Initialize Railway project (if not already done)
echo "Run these commands manually:"
echo "1. railway login"
echo "2. railway link (select your existing project or create new)"
echo "3. Set environment variables in Railway dashboard"
echo ""

echo "📋 Required Environment Variables for Railway:"
echo "=============================================="
echo ""
echo "NODE_ENV=production"
echo "PORT=8080"
echo "MONGO_URL=mongodb+srv://abhisheksingh4115:<EMAIL>/Practice-db?retryWrites=true&w=majority"
echo "SECRET_KEY=iREJP1r+kdPWJ7VWcz/BtXibAWhFTEIKnWdGiZ9ZAI4="
echo "ADMIN_USER=<EMAIL>"
echo "ADMIN_HASHED_PASS=\$2b\$10\$kthSu.4WHdClXAhEdcEbTeChxrfKC0hqidI3sDJv3uDTTa56KjbR2"
echo "CLOUDINARY_CLOUD_NAME=drujwoine"
echo "CLOUDINARY_API_KEY=683974564338127"
echo "CLOUDINARY_API_SECRET=J8UG31wXEm-reuECSmdmQbSy3c4"
echo "FRONTEND_URL=https://guideway-consulting.vercel.app"
echo "RECAPTCHA_SECRET_KEY=6Ld-zC4rAAAAAAVVbpXmfeq9st9SZh0nT-BAceSS"
echo "GOOGLE_CLIENT_ID=************-43j6p7k79nfi39d5u7knmai9nkla1tgb.apps.googleusercontent.com"
echo "BREVO_SMTP_HOST=smtp-relay.brevo.com"
echo "BREVO_SMTP_PORT=587"
echo "BREVO_SMTP_USER=<EMAIL>"
echo "BREVO_SMTP_PASS=6rnTYQPSdMafC8Wq"
echo "BREVO_SEND_EMAIL=<EMAIL>"
echo "BREVO_API_KEY=xkeysib-9ac78f59c620393b66c53ef7c0d0b12f59c4b20ddb58c9a40f112ba2d96ac7-vw0bNZ2V2DccA7iK"
echo "STRIPE_SECRET_KEY=sk_test_51RU2HJPFPsuRFy0ndsdzUcivQi0Q2PR4DCjtyRBsloQSfCAI1COvf9O4PV4tCnjk93sOpsuQJa5x3NdybecqrWAa00IYq3WbZN"
echo "STRIPE_WEBHOOK_SECRET=whsec_9rCtrGn1a9YQrals5gpJWjgdErLwZSdv"
echo ""

echo "🚀 After setting environment variables, deploy with:"
echo "railway up"
echo ""

echo "🔍 Monitor deployment:"
echo "railway logs"
echo ""

echo "✅ Setup complete!"
echo "Your backend will be available at: https://your-project-name.up.railway.app"
