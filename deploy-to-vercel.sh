#!/bin/bash

# 🌐 Vercel Deployment Script for Guideway Consulting Frontend
echo "🌐 Deploying Guideway Consulting Frontend to Vercel"
echo "=================================================="
echo ""

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI not found. Installing..."
    npm install -g vercel
fi

echo "✅ Vercel CLI found"
echo ""

# Navigate to frontend directory
cd frontend

echo "🔧 Setting up Vercel project..."
echo ""

# Update environment variables for production
echo "📝 Updating environment variables for production..."

# Create production environment file
cat > .env.production << EOF
VITE_BACKEND_URL=https://guideway-consulting-production.up.railway.app/api
VITE_RECAPTCHA_SITE_KEY=6Ld-zC4rAAAAAPjAuASmaRY-J4hie_YZPF63OuHo
VITE_GOOGLE_CLIENT_ID=************-43j6p7k79nfi39d5u7knmai9nkla1tgb.apps.googleusercontent.com
EOF

echo "✅ Production environment file created"
echo ""

echo "🏗️ Building project..."
npm run build

echo ""
echo "🚀 Deploying to Vercel..."
echo ""

echo "Run these commands manually:"
echo "1. vercel login"
echo "2. vercel --prod"
echo ""

echo "📋 Environment Variables to set in Vercel Dashboard:"
echo "=================================================="
echo ""
echo "VITE_BACKEND_URL=https://guideway-consulting-production.up.railway.app/api"
echo "VITE_RECAPTCHA_SITE_KEY=6Ld-zC4rAAAAAPjAuASmaRY-J4hie_YZPF63OuHo"
echo "VITE_GOOGLE_CLIENT_ID=************-43j6p7k79nfi39d5u7knmai9nkla1tgb.apps.googleusercontent.com"
echo ""

echo "🔧 Vercel Configuration:"
echo "- Build Command: npm run build"
echo "- Output Directory: dist"
echo "- Install Command: npm install"
echo ""

echo "✅ Setup complete!"
echo "Your frontend will be available at: https://guideway-consulting.vercel.app"
