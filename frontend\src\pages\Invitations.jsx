import React, { useMemo, useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Mail,
  Clock,
  Gift,
  DollarSign,
  ArrowRight,
  X,
  CheckCircle2,
  BadgePercent,
} from "lucide-react";
import <PERSON><PERSON> from "js-cookie";
import config from "../config";

const fmtCurrency = (n) =>
  typeof n === "number"
    ? new Intl.NumberFormat(undefined, {
        style: "currency",
        currency: "USD",
        maximumFractionDigits: 0,
      }).format(n)
    : "";

const relTime = (iso) => {
  const now = new Date();
  const t = new Date(iso);
  const diff = t.getTime() - now.getTime();
  const abs = Math.abs(diff);
  const mins = Math.round(abs / 60000);
  if (mins < 60) return diff < 0 ? `${mins}m ago` : `in ${mins}m`;
  const hours = Math.round(mins / 60);
  if (hours < 24) return diff < 0 ? `${hours}h ago` : `in ${hours}h`;
  const days = Math.round(hours / 24);
  return diff < 0 ? `${days}d ago` : `in ${days}d`;
};

const deadlineLabel = (iso) => {
  const d = new Date(iso);
  const rel = relTime(iso);
  return { text: `${d.toLocaleDateString()} (${rel})`, full: d.toString() };
};

function Modal({ open, onClose, title, children }) {
  return (
    <AnimatePresence>
      {open && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <div className="absolute inset-0 bg-black/60" onClick={onClose} />
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 20, opacity: 0 }}
            className="relative z-10 w-[92vw] max-w-md rounded-2xl bg-gray-900 text-gray-100 p-5 shadow-2xl"
          >
            <div className="mb-3 flex items-center justify-between">
              <h3 className="text-lg font-semibold">{title}</h3>
              <button
                onClick={onClose}
                className="rounded-full p-1 hover:bg-gray-800"
                aria-label="Close"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="text-sm text-gray-300">{children}</div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

function InvitationCard({ invite, onAccept, onDecline }) {
  const [showPayout, setShowPayout] = useState(false);
  const deadline = deadlineLabel(invite.deadlineISO);
  const isBidding = invite.type === "Bidding";
  const isFixed = invite.type === "Fixed";
  const isGifted = invite.type === "Gifted";

  return (
    <motion.div
      layout
      className="rounded-2xl bg-neutral-900 p-5 shadow-lg ring-1 ring-neutral-700 hover:ring-blue-500 transition"
    >
      {invite.status === "Accepted" && (
        <div className="mb-3 flex items-center gap-2 rounded-xl bg-green-900/40 px-3 py-2 text-green-400">
          <CheckCircle2 className="h-5 w-5" />
          <span className="font-medium">You joined this campaign!</span>
        </div>
      )}

      <div className="flex items-start gap-4">
        <img
          src={invite.brandLogo}
          alt={`${invite.brandName} logo`}
          className="h-12 w-12 rounded-xl object-cover ring-1 ring-gray-700"
        />
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white">
            {invite.brandName}
          </h3>
          <div className="mt-1 text-sm text-gray-400">
            <div className="flex items-center gap-2">
              <Gift className="h-4 w-4" />
              <span>
                <span className="font-medium">Campaign:</span> <br />{" "}
                {invite.campaignName}
              </span>
            </div>
            <div className="mt-1 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span title={deadline.full}>
                <span className="font-medium">Deadline:</span> {deadline.text}
              </span>
            </div>
          </div>
          <div className="mt-3 rounded-xl bg-neutral-800 p-3 text-sm">
            <div className="mb-1 flex items-center gap-2 font-medium">
              <DollarSign className="h-4 w-4" /> Reward
              {isBidding && (
                <span className="ml-1 inline-flex items-center gap-1 rounded-full bg-yellow-800 px-2 py-0.5 text-xs font-semibold text-yellow-200">
                  <BadgePercent className="h-3 w-3" /> Bidding
                </span>
              )}
            </div>
            {isFixed && (
              <div className="text-gray-300">
                Product +
                <button
                  className="ml-1 underline decoration-dotted underline-offset-2 hover:text-blue-400"
                  onClick={() => setShowPayout(true)}
                >
                  {fmtCurrency(invite.rewardUsd)}
                </button>
              </div>
            )}
            {isBidding && (
              <div className="text-gray-300">
                Product <span className="text-gray-500">(price hidden)</span>
              </div>
            )}
            {isGifted && (
              <div className="text-gray-300">
                Product{invite.shippingIncluded ? " + Free Shipping" : ""}
              </div>
            )}
          </div>
        </div>
      </div>

      {invite.status === "Pending" && (
        <div className="mt-4 flex flex-col gap-2 sm:flex-row items-center justify-between">
          <button
            onClick={() => onAccept(invite.id)}
            className="inline-flex items-center justify-center gap-2 rounded-2xl bg-green-600 px-5 py-2.5 font-semibold text-white hover:bg-green-700"
          >
            <CheckCircle2 className="h-5 w-5" /> Accept
          </button>
          <button
            onClick={() => onDecline(invite.id)}
            className="inline-flex items-center justify-center gap-2 rounded-2xl bg-red-900/40 px-5 py-2.5 font-semibold text-red-300 hover:bg-red-900"
          >
            <X className="h-5 w-5" /> Decline
          </button>
        </div>
      )}

      <Modal
        open={showPayout}
        onClose={() => setShowPayout(false)}
        title="Payout Details"
      >
        <p className="mb-3">
          You will receive <strong>{fmtCurrency(invite.rewardUsd)}</strong> upon
          completion per campaign terms.
        </p>
        <ul className="list-disc pl-5 text-gray-300">
          <li>Payment method: Bank transfer</li>
          <li>Typical processing: 5–7 business days</li>
          <li>Taxes may apply based on your location</li>
        </ul>
      </Modal>
    </motion.div>
  );
}

export default function InvitationsPage() {
  const [invites, setInvites] = useState([]);
  const [loading, setLoading] = useState(true);
  const token = Cookie.get("token");
  // Fetch invites from backend
  useEffect(() => {
    const fetchInvites = async () => {
      try {
        const res = await fetch(`${config.BACKEND_URL}/invitations`, {
          headers: {
            Authorization:token,
          },
        });
        const data = await res.json();
        if (data.status === "success") {
          setInvites(
            data.data.map((inv) => ({
              id: inv._id,
              brandLogo: inv.campaign.brand.logo,
              brandName: inv.campaign.brand.name,
              campaignName: inv.campaign.name,
              deadlineISO: inv.campaign.deadline,
              type: inv.campaign.type,
              rewardUsd: inv.campaign.reward,
              shippingIncluded: inv.campaign.shippingIncluded,
              status: inv.status.charAt(0).toUpperCase() + inv.status.slice(1), // pending → Pending
            }))
          );
        }
      } catch (err) {
        console.error("Failed to load invites", err);
      } finally {
        setLoading(false);
      }
    };

    fetchInvites();
  }, []);

  const visibleInvites = useMemo(
    () => invites.filter((i) => i.status !== "Declined" && i.status !== "Expired"),
    [invites]
  );
  const pendingCount = visibleInvites.filter((i) => i.status === "Pending").length;

  const handleAccept = async (id) => {
    try {
      const res = await fetch(`${config.BACKEND_URL}/invitations/${id}/accept`, {
        method: "PATCH",
        headers: { Authorization: token },
      });
      const data = await res.json();
      if (data.status === "success") {
        setInvites((prev) =>
          prev.map((i) => (i.id === id ? { ...i, status: "Accepted" } : i))
        );
      }
    } catch (err) {
      console.error("Accept error", err);
    }
  };

  const handleDecline = async (id) => {
    try {
      const res = await fetch(`${config.BACKEND_URL}/invitations/${id}/decline`, {
        method: "PATCH",
        headers: { Authorization:token},
      });
      const data = await res.json();
      if (data.status === "success") {
        setInvites((prev) =>
          prev.map((i) => (i.id === id ? { ...i, status: "Declined" } : i))
        );
      }
    } catch (err) {
      console.error("Decline error", err);
    }
  };

  if (loading) {
    return (
      <div className="mx-auto max-w-6xl px-4 py-8 min-h-screen text-gray-400 text-center">
        Loading invitations...
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-6xl px-4 py-8 min-h-screen text-gray-100">
      <div className="mb-6 flex items-end justify-between">
        <div>
          <h1 className="text-2xl font-bold">Invitations</h1>
          <p className="mt-1 text-sm text-gray-400">
            These campaigns were sent to you by brands who want to work with you.
          </p>
        </div>
        <div className="rounded-full bg-neutral-800 px-4 py-2 text-sm font-semibold text-neutral-300 ring-1 ring-neutral-800">
          Invitations ({pendingCount})
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <AnimatePresence>
          {visibleInvites.length === 0 ? (
            <motion.div
              key="empty"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="col-span-full rounded-2xl border border-dashed border-gray-700 bg-gray-800 p-10 text-center text-gray-400"
            >
              <Mail className="mx-auto mb-3 h-8 w-8" />
              <p className="font-medium">No invitations yet.</p>
              <a
                href="/campaigns/gifted"
                className="mt-2 inline-flex items-center gap-1 text-blue-400 underline"
              >
                Browse campaigns <ArrowRight className="h-4 w-4" />
              </a>
            </motion.div>
          ) : (
            visibleInvites.map((invite) => (
              <motion.div
                key={invite.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                <InvitationCard
                  invite={invite}
                  onAccept={handleAccept}
                  onDecline={handleDecline}
                />
              </motion.div>
            ))
          )}
        </AnimatePresence>
      </div>

      <p className="mt-8 text-xs text-gray-500">
        Tip: Deadline shows relative time with a full timestamp on hover.
      </p>
    </div>
  );
}
