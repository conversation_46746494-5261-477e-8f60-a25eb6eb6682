import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { Helmet } from "react-helmet";
import Cookies from "js-cookie";
import { FaCopy, FaGift, FaLink, FaStar } from "react-icons/fa";
import config from "../config";

const ReferralRewards = () => {
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [redeemLoading, setRedeemLoading] = useState(null);

  useEffect(() => {
    fetchUserRewards();
  }, []);

  const fetchUserRewards = async () => {
    const token = Cookies.get("token") || localStorage.getItem("token");
    if (!token) {
      toast.error("User not logged in");
      setLoading(false);
      return;
    }
    try {
      const res = await fetch(`${config.BACKEND_URL}/user/campaigns/rewards`, {
        headers: { Authorization: token },
      });
      const data = await res.json();
      if (data.status === "success") setUserData(data);
      else toast.error(data.message || "Failed to load rewards");
    } catch (err) {
      toast.error("Error fetching rewards");
    } finally {
      setLoading(false);
    }
  };

  const handleRedeem = async (tierId) => {
    const token = Cookies.get("token") || localStorage.getItem("token");
    if (!token) return toast.error("User not logged in");

    try {
      setRedeemLoading(tierId);
      const res = await fetch(`${config.BACKEND_URL}/user/campaigns/redeem`, {
        method: "POST",
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ tierId }),
      });
      const data = await res.json();
      if (data.status === "success") {
        toast.success(data.message || "Reward requested");
        fetchUserRewards();
      } else {
        toast.error(data.message || "Redeem failed");
      }
    } catch {
      toast.error("Redeem failed");
    } finally {
      setRedeemLoading(null);
    }
  };

  if (loading) return <div className="text-white text-center mt-10">Loading...</div>;

  const { approvedCount, tiers, referrals, history, referralLink, progress } = userData;

  return (
    <div className="max-w-5xl mx-auto px-4 sm:px-6 py-10 text-white">
      <Helmet>
        <title>Referral & Rewards | Matchably</title>
      </Helmet>

      <h2 className="text-3xl font-bold mb-6 flex items-center gap-2">
        <FaGift className="text-yellow-400" /> Referral & Rewards
      </h2>

      {/* Approved Posts */}
      <div className="bg-[#1c1c1c] p-5 rounded-lg mb-10 shadow-md">
        <h3 className="text-lg font-semibold mb-3">
          Your Approved Posts: {approvedCount}
        </h3>
        <p className="text-gray-400">
        Reach next tier to unlock rewards! ({progress})
        </p>
      </div>

      {/* Reward Tier Cards */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        {tiers.map((tier) => {
          const statusClass =
            tier.status === "claimed"
              ? "bg-green-600"
              : tier.status === "in_progress"
              ? "bg-blue-600"
              : "bg-gray-600";
          return (
            <div key={tier.id} className="bg-[#2a2a2a] p-4 rounded shadow-md">
              <p className="font-semibold text-lg mb-1">
                {tier.id === 11 ? "11+" : `${tier.id} Approvals`}
              </p>
              <p className="text-sm text-gray-400 mb-3">Reward: {tier.reward}</p>
              <button
                className={`px-4 py-2 rounded text-sm w-full transition-all duration-200 ${
                  tier.status === "in_progress" ? "bg-green-600 hover:bg-green-700" : statusClass
                }`}
                disabled={redeemLoading === tier.id || tier.status !== "in_progress"}
                onClick={() => handleRedeem(tier.id)}
              >
                {redeemLoading === tier.id
                  ? "Processing..."
                  : tier.status === "claimed"
                  ? "✅ Claimed"
                  : tier.status === "in_progress"
                  ? "Redeem"
                  : "🔒 Locked"}
              </button>
            </div>
          );
        })}
      </div>

      {/* Referral Bonus Section */}
      <div className="bg-[#1c1c1c] p-5 rounded-lg mb-10 shadow-md">
        <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
          <FaLink className="text-blue-400" /> Your Referral Link
        </h3>
        <div className="flex items-center gap-2">
          <input
            className="bg-gray-800 p-2 rounded w-full text-sm"
            value={referralLink}
            readOnly
          />
          <button
            className="bg-indigo-600 hover:bg-indigo-700 px-3 py-2 rounded flex items-center gap-1 text-sm"
            onClick={() => {
              navigator.clipboard.writeText(referralLink);
              toast.success("Link copied");
            }}
          >
            <FaCopy /> Copy
          </button>
        </div>

        <table className="w-full text-sm mt-5">
          <thead>
            <tr className="text-left border-b border-gray-600 text-gray-300">
              <th className="py-2">Email</th>
              <th>Status</th>
              <th>Bonus</th>
            </tr>
          </thead>
          <tbody>
            {referrals.length ? (
              referrals.map((r, i) => (
                <tr key={i} className="border-b border-gray-700 hover:bg-gray-800">
                  <td className="py-2">{r.email}</td>
                  <td>{r.status}</td>
                  <td>{r.bonus}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="3" className="text-center text-gray-500 py-4">
                  No referrals yet.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Reward History */}
      <div className="bg-[#1c1c1c] p-5 rounded-lg shadow-md">
        <h4 className="font-semibold mb-3 text-gray-200">Reward History</h4>
        <div className="overflow-x-auto">
          <table className="w-full text-sm border border-gray-700">
            <thead className="bg-gray-700 text-gray-200">
              <tr>
                <th className="py-2 px-4 text-left">Date</th>
                <th className="py-2 px-4 text-left">Type</th>
                <th className="py-2 px-4 text-left">Campaign</th>
                <th className="py-2 px-4 text-left">Reward</th>
              </tr>
            </thead>
            <tbody>
              {history.length ? (
                history.map((item, i) => (
                  <tr key={i} className="border-t border-gray-700">
                    <td className="py-2 px-4">{new Date(item.date).toLocaleString()}</td>
                    <td className="py-2 px-4">{item.type}</td>
                    <td className="py-2 px-4">{item.campaign}</td>
                    <td className="py-2 px-4">{item.reward}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="4" className="text-center text-gray-500 py-4">
                    No reward activity yet.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ReferralRewards;
