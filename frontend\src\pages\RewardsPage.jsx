/** @format */

import React from "react";
import { <PERSON> } from "react-router-dom";
import { Helmet } from "react-helmet";

const RewardsPage = () => {
  const tiers = [
    { count: 1, reward: "$10", note: "Instant reward" },
    { count: 3, reward: "$25", note: "Includes $15 bonus" },
    { count: 5, reward: "$55", note: "Includes $30 bonus" },
    { count: 10, reward: "$110", note: "Includes $55 bonus" },
    { count: 11, reward: "+$5 per approval", note: "Ongoing beyond 10" },
  ];

  const faqs = [
    [
      "How fast do I get my rewards?",
      "Once you hit a milestone, your Amazon e-Gift Card will be delivered within 48 hours.",
    ],
    [
      "What qualifies as an approved post?",
      "Only posts explicitly marked as Approved by the brand count.",
    ],
    [
      "Can I receive rewards multiple times?",
      "Yes! Rewards are cumulative — you unlock new gift cards at each milestone.",
    ],
    [
      "Can I invite multiple friends?",
      "Absolutely! Every invited friend who gets 1 approved post gives you +1 approval credit.",
    ],
  ];

  return (
    <div className="bg-black text-white">
      {/* SEO */}
      <Helmet>
        <title>Matchably Rewards</title>
        <meta
          name="description"
          content="Earn Amazon e-Gift Cards just by getting your content approved. Invite friends and unlock bigger rewards with Matchably!"
        />
        <meta property="og:title" content="Matchably Creator Rewards" />
        <meta
          property="og:description"
          content="Turn approved posts into Amazon gift cards. Invite friends and boost your rewards!"
        />
      </Helmet>

      {/* Hero */}
      <section className="text-center py-24 md:py-32 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-extrabold mb-6 leading-tight">
            💰 Get Rewarded for Your Content
          </h1>
          <p className="text-lg md:text-xl text-gray-400 mb-10 max-w-2xl mx-auto">
            Each approved post counts toward your reward tier. Invite friends,
            grow faster, and redeem Amazon e-Gift Cards automatically.
          </p>
          <Link
            to="/campaigns"
            className="inline-block px-10 py-5 text-lg font-semibold rounded-xl bg-gradient-to-r from-green-400 to-blue-400 text-black transition-all duration-300 transform hover:scale-110 hover:shadow-[0_0_30px_#00ffcc]"
          >
            🎯 Start Earning Now
          </Link>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-24 px-4 border-t border-gray-800">
        <div className="max-w-5xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-12">🎯 How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            {[
              {
                icon: "📸",
                title: "Apply & Submit",
                desc: "Join campaigns and upload content",
              },
              {
                icon: "✅",
                title: "Get Approved",
                desc: "Each approved post increases your reward tier",
              },
              {
                icon: "💳",
                title: "Receive Rewards",
                desc: "Amazon e-Gift Cards sent automatically",
              },
            ].map((s, i) => (
              <div
                key={i}
                className="p-8 bg-[#111] rounded-xl border border-gray-700 hover:border-blue-400 shadow-lg hover:shadow-blue-500/30 text-center transition-all duration-300 hover:scale-105"
              >
                <div className="text-5xl mb-4">{s.icon}</div>
                <h3 className="text-xl font-semibold text-green-400 mb-2">
                  {s.title}
                </h3>
                <p className="text-gray-300">{s.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Reward Tiers */}
      <section className="py-24 px-4 border-t border-gray-800">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-6">
            🎁 Creator Reward Tiers
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {tiers.map((tier, i) => (
              <div
                key={i}
                className="p-6 bg-[#111] rounded-xl border border-gray-700 text-center shadow-md hover:border-blue-400 hover:shadow-blue-500/30 transition-all duration-300"
              >
                <h3 className="text-lg font-bold text-green-400 mb-2">
                  {tier.count === 11 ? "11+ Approvals" : `${tier.count} Approval${tier.count > 1 ? "s" : ""}`}
                </h3>
                <p className="text-xl font-semibold mb-2">{tier.reward}</p>
                <p className="text-sm text-gray-400">{tier.note}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Referral Rewards */}
      <section className="py-24 px-4 border-t border-gray-800">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">👥 Invite Friends, Earn More</h2>
          <p className="text-gray-400 mb-10">
            Every friend who gets 1 post approved = +1 approval credit for you.
          </p>
          <p className="text-sm text-gray-400 max-w-xl mx-auto">
            Example: Invite 4 friends, they each get 1 approved post → you
            instantly gain +4 credits and unlock rewards faster!
          </p>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-24 px-4 border-t border-gray-800">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">
            ❓ Frequently Asked Questions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {faqs.map(([q, a], i) => (
              <div
                key={i}
                className="p-6 bg-[#111] rounded-xl border border-gray-700 shadow-md hover:shadow-blue-500/30 hover:border-blue-400 transition-all duration-300"
              >
                <h3 className="text-lg font-semibold text-green-400 mb-2">
                  {q}
                </h3>
                <p className="text-sm text-gray-300">{a}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 px-4 border-t border-gray-800 text-center">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to start earning?
          </h2>
          <Link
            to="/campaigns"
            className="inline-block px-10 py-5 text-lg font-semibold rounded-xl bg-gradient-to-r from-green-400 to-blue-400 text-black shadow-xl transition-all duration-300 transform hover:scale-110 hover:shadow-[0_0_35px_#00ffff]"
          >
            📩 Apply Now
          </Link>
          <p className="mt-4 text-sm text-gray-400">
            Or{" "}
            <Link to="/campaigns" className="text-blue-400 underline">
              browse active campaigns
            </Link>
          </p>
          <p className="mt-2 text-xs text-gray-500">
            ✅ Rewards issued as Amazon e-Gift Cards.
          </p>
        </div>
      </section>
    </div>
  );
};

export default RewardsPage;
