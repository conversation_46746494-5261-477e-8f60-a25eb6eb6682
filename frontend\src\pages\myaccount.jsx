import React, { useState } from "react";
import { Helmet } from "react-helmet";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>aInstagram,
  FaTiktok,
  FaLock,
  FaCheckCircle,
  FaExclamationCircle,
} from "react-icons/fa";
import { Dialog } from "@headlessui/react";
import config from "../config";
import { toast } from "react-toastify";
import Cookies from "js-cookie";
import "react-toastify/dist/ReactToastify.css";
import { Link } from "react-router-dom";

const MyAccount = ({ user }) => {
  const [socialLinks, setSocialLinks] = useState({
    instagramId: user.instagramId || "",
    tiktokId: user.tiktokId || "",
  });

  const [passwords, setPasswords] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setSocialLinks((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    try {
      const token = Cookies.get("token") || localStorage.getItem("token");
      const response = await fetch(
        `${config.BACKEND_URL}/user/campaigns/update-social`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: token,
          },
          body: JSON.stringify(socialLinks),
        }
      );

      const data = await response.json();

      if (response.ok) {
        toast.success(data.message || "Social links updated!", { theme: "dark" });
        user.instagramId = socialLinks.instagramId;
        user.tiktokId = socialLinks.tiktokId;
      } else {
        toast.error(data.message || "Update failed.", { theme: "dark" });
      }
    } catch (error) {
      console.error("Save error:", error);
      toast.error("Something went wrong while saving.");
    }
  };

  const handlePasswordChange = async () => {
    const { oldPassword, newPassword, confirmPassword } = passwords;

    if (!oldPassword || !newPassword || !confirmPassword) {
      return toast.error("Please fill in all password fields.");
    }

    if (newPassword !== confirmPassword) {
      return toast.error("New passwords do not match.");
    }

    try {
      const token = Cookies.get("token") || localStorage.getItem("token");
      const response = await fetch(
        `${config.BACKEND_URL}/user/campaigns/change-password`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: token,
          },
          body: JSON.stringify({ oldPassword, newPassword, confirmPassword }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        toast.success(data.message || "Password changed successfully!", {
          theme: "dark",
          autoClose: 3000,
          position: "top-right",
        });
        setPasswords({ oldPassword: "", newPassword: "", confirmPassword: "" });
        setIsModalOpen(false);
      } else {
        toast.error(data.message || "Failed to change password.", {
          theme: "dark",
          autoClose: 3000,
          position: "top-right",
        });
      }
    } catch (error) {
      console.error("Password change error:", error);
      toast.error("Something went wrong while changing the password.");
    }
  };

  return (
    <div className="min-h-screen bg-black text-gray-100 p-6">
      <Helmet>
        <title>My Account</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="max-w-4xl mx-auto bg-[#171717] rounded-2xl shadow-lg p-8">
        <div className="flex items-center justify-between border-b border-gray-700 pb-4 mb-6">
          <div className="flex items-center space-x-4">
            <FaUser className="text-3xl text-blue-500" />
            <h1 className="text-2xl font-bold text-white">My Profile</h1>
          </div>
        </div>

        {/* Grid Layout */}
        <div className="grid md:grid-cols-2 gap-8">
          {/* Left - User Info */}
          <div>
            <h2 className="text-lg font-semibold text-blue-400 mb-3">User Info</h2>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <FaUser className="text-blue-400" />
                <span>{user.name}</span>
              </div>
              <div className="flex items-center gap-3 break-all">
                <FaEnvelope className="text-blue-400" />
                <span>{user.email}</span>
              </div>
            </div>

            {/* Social Profiles */}
            <h2 className="text-lg font-semibold text-blue-400 mt-6 mb-3">
              Social Profiles
            </h2>
            <div className="space-y-4">
              {/* Instagram */}
              <div className="flex items-center gap-3">
                <FaInstagram className="text-pink-500" />
                {user.instagramId ? (
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{user.instagramId}</span>
                    <FaCheckCircle className="text-green-500" title="Connected" />
                  </div>
                ) : (
                    <Link
                    to="/onboarding?step=2"
                    className="px-3 py-3  flex items-center justify-center gap-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm"
                  >
                    <FaExclamationCircle /> Connect Now
              </Link>
                )}
              </div>

              {/* TikTok */}
              <div className="flex items-center gap-3">
                <FaTiktok className="text-gray-300" />
                {user.tiktokId ? (
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{user.tiktokId}</span>
                    <FaCheckCircle className="text-green-500" title="Connected" />
                  </div>
                ) : (
                  <Link
                    to="/onboarding?step=2"
                      className="px-3 py-3  flex items-center justify-center gap-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm"
               >
                    <FaExclamationCircle /> Connect Now
                  </Link>
                )}
              </div>

              {/* Edit Inputs */}
              <div className="mt-4 space-y-3">
                <input
                  type="text"
                  name="instagramId"
                  value={socialLinks.instagramId}
                  onChange={handleChange}
                  placeholder="Instagram ID"
                  className="w-full bg-gray-800 p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="text"
                  name="tiktokId"
                  value={socialLinks.tiktokId}
                  onChange={handleChange}
                  placeholder="TikTok ID"
                  className="w-full bg-gray-800 p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  onClick={handleSave}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 rounded-md transition"
                >
                  Save Social Profiles
                </button>
              </div>
            </div>
          </div>

          {/* Right - Password Section */}
          <div className="flex flex-col justify-between">
            <div className="mt-8 md:mt-0">
              <h2 className="text-lg font-semibold text-blue-400 mb-4">Security</h2>
              <button
                onClick={() => setIsModalOpen(true)}
                className="w-full bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 rounded-md transition"
              >
                Change Password
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Password Modal */}
      <Dialog open={isModalOpen} onClose={() => setIsModalOpen(false)} className="relative z-50">
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="w-full max-w-md bg-[#1f1f1f] text-white rounded-xl p-6 shadow-xl">
            <Dialog.Title className="text-xl font-semibold text-blue-400 mb-4">
              Change Password
            </Dialog.Title>
            <div className="space-y-4">
              {["oldPassword", "newPassword", "confirmPassword"].map((field, idx) => (
                <div className="flex items-center gap-3" key={idx}>
                  <FaLock className="text-blue-400" />
                  <input
                    type="password"
                    placeholder={
                      field === "oldPassword"
                        ? "Old Password"
                        : field === "newPassword"
                        ? "New Password"
                        : "Confirm New Password"
                    }
                    className="flex-1 bg-gray-800 p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={passwords[field]}
                    onChange={(e) =>
                      setPasswords({ ...passwords, [field]: e.target.value })
                    }
                  />
                </div>
              ))}
              <div className="flex justify-end gap-2 pt-2">
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 rounded-md bg-gray-600 hover:bg-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handlePasswordChange}
                  className="px-4 py-2 rounded-md bg-blue-500 hover:bg-blue-600 text-white"
                >
                  Update
                </button>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  );
};

export default MyAccount;
