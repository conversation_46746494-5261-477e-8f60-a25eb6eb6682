#!/usr/bin/env node

// 🧪 Deployment Test Script
const axios = require('axios');

const FRONTEND_URL = 'https://guideway-consulting.vercel.app';
const BACKEND_URL = 'https://guideway-consulting-production.up.railway.app';

console.log('🧪 Testing Guideway Consulting Deployment');
console.log('=========================================');
console.log(`Frontend: ${FRONTEND_URL}`);
console.log(`Backend:  ${BACKEND_URL}`);
console.log('');

async function testBackend() {
  console.log('🔍 Testing Backend...');
  
  try {
    // Test health endpoint
    console.log('  ✅ Testing health endpoint...');
    const healthResponse = await axios.get(`${BACKEND_URL}/health`, {
      timeout: 10000
    });
    console.log('  ✅ Health check passed:', healthResponse.data);
    
    // Test root endpoint
    console.log('  ✅ Testing root endpoint...');
    const rootResponse = await axios.get(`${BACKEND_URL}/`, {
      timeout: 10000
    });
    console.log('  ✅ Root endpoint accessible');
    
    // Test API endpoint
    console.log('  ✅ Testing API endpoint...');
    const apiResponse = await axios.get(`${BACKEND_URL}/api`, {
      timeout: 10000
    });
    console.log('  ✅ API endpoint accessible');
    
    return true;
  } catch (error) {
    console.log('  ❌ Backend test failed:', error.message);
    if (error.response) {
      console.log('  📊 Status:', error.response.status);
      console.log('  📊 Data:', error.response.data);
    }
    return false;
  }
}

async function testFrontend() {
  console.log('🔍 Testing Frontend...');
  
  try {
    const response = await axios.get(FRONTEND_URL, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    if (response.status === 200) {
      console.log('  ✅ Frontend accessible');
      console.log('  📊 Status:', response.status);
      return true;
    } else {
      console.log('  ❌ Frontend returned status:', response.status);
      return false;
    }
  } catch (error) {
    console.log('  ❌ Frontend test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting deployment tests...\n');
  
  const backendResult = await testBackend();
  console.log('');
  
  const frontendResult = await testFrontend();
  console.log('');
  
  console.log('📊 Test Results:');
  console.log('================');
  console.log(`Backend:  ${backendResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Frontend: ${frontendResult ? '✅ PASS' : '❌ FAIL'}`);
  
  if (backendResult && frontendResult) {
    console.log('\n🎉 All tests passed! Deployment is working correctly.');
    console.log('\n🔗 URLs:');
    console.log(`   Frontend: ${FRONTEND_URL}`);
    console.log(`   Backend:  ${BACKEND_URL}`);
    console.log(`   Health:   ${BACKEND_URL}/health`);
  } else {
    console.log('\n⚠️  Some tests failed. Please check the deployment.');
  }
}

// Run tests
runTests().catch(console.error);
