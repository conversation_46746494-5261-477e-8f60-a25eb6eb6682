#!/usr/bin/env node

// Deployment Verification Script
const axios = require('axios');

const FRONTEND_URL = 'https://guideway-consulting.vercel.app';
const BACKEND_URL = process.argv[2] || 'https://guideway-consulting-production.up.railway.app';

console.log('🔍 Verifying Guideway Consulting Deployment');
console.log('==========================================');
console.log(`Frontend: ${FRONTEND_URL}`);
console.log(`Backend:  ${BACKEND_URL}`);
console.log('');

async function verifyDeployment() {
  let allPassed = true;

  // Test 1: Backend Health Check
  console.log('🏥 Test 1: Backend Health Check');
  try {
    const response = await axios.get(`${BACKEND_URL}/health`, { timeout: 10000 });
    if (response.status === 200 && response.data.status === 'healthy') {
      console.log('   ✅ Backend is healthy');
      console.log(`   📊 Database: ${response.data.database}`);
      console.log(`   🌍 Environment: ${response.data.environment}`);
      console.log(`   🔌 Port: ${response.data.port}`);
    } else {
      console.log('   ❌ Backend health check failed');
      console.log('   📄 Response:', response.data);
      allPassed = false;
    }
  } catch (error) {
    console.log('   ❌ Backend health check failed');
    console.log(`   🚨 Error: ${error.message}`);
    allPassed = false;
  }
  console.log('');

  // Test 2: Backend Root Endpoint
  console.log('🌐 Test 2: Backend Root Endpoint');
  try {
    const response = await axios.get(`${BACKEND_URL}/`, { timeout: 10000 });
    if (response.status === 200) {
      console.log('   ✅ Root endpoint accessible');
      console.log(`   📱 App: ${response.data.app}`);
    } else {
      console.log('   ❌ Root endpoint failed');
      allPassed = false;
    }
  } catch (error) {
    console.log('   ❌ Root endpoint failed');
    console.log(`   🚨 Error: ${error.message}`);
    allPassed = false;
  }
  console.log('');

  // Test 3: CORS Check (simulate frontend request)
  console.log('🔒 Test 3: CORS Configuration');
  try {
    const response = await axios.get(`${BACKEND_URL}/health`, {
      headers: {
        'Origin': FRONTEND_URL,
        'Access-Control-Request-Method': 'GET',
      },
      timeout: 10000
    });
    console.log('   ✅ CORS configured correctly');
  } catch (error) {
    if (error.response && error.response.status === 403) {
      console.log('   ❌ CORS blocking frontend requests');
      console.log('   🔧 Check FRONTEND_URL in Railway environment variables');
      allPassed = false;
    } else {
      console.log('   ⚠️  CORS test inconclusive');
    }
  }
  console.log('');

  // Test 4: Frontend Accessibility
  console.log('🎨 Test 4: Frontend Accessibility');
  try {
    const response = await axios.get(FRONTEND_URL, { timeout: 10000 });
    if (response.status === 200) {
      console.log('   ✅ Frontend is accessible');
    } else {
      console.log('   ❌ Frontend not accessible');
      allPassed = false;
    }
  } catch (error) {
    console.log('   ❌ Frontend not accessible');
    console.log(`   🚨 Error: ${error.message}`);
    allPassed = false;
  }
  console.log('');

  // Summary
  console.log('📋 Deployment Verification Summary');
  console.log('==================================');
  if (allPassed) {
    console.log('🎉 All tests passed! Deployment is successful.');
    console.log('');
    console.log('🔗 Your application is live at:');
    console.log(`   Frontend: ${FRONTEND_URL}`);
    console.log(`   Backend:  ${BACKEND_URL}`);
    console.log('');
    console.log('🧪 Next steps:');
    console.log('   1. Test user registration/login');
    console.log('   2. Test campaign creation');
    console.log('   3. Test admin panel access');
    console.log('   4. Monitor application logs');
  } else {
    console.log('❌ Some tests failed. Please check the issues above.');
    console.log('');
    console.log('🔧 Common fixes:');
    console.log('   - Verify all environment variables in Railway');
    console.log('   - Check MongoDB connection');
    console.log('   - Ensure CORS is configured correctly');
    console.log('   - Wait a few minutes for deployment to stabilize');
  }
}

// Run verification
verifyDeployment().catch(error => {
  console.error('🚨 Verification script failed:', error.message);
  process.exit(1);
});
